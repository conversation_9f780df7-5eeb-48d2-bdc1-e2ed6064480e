import React from 'react'
import useStyles from '../../Settings.styles'
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Skeleton
} from '@mui/material'
import AmexImg from '../../../../components/ui-kit/icons/svg/AmexImg.svg'
import MasterImg from '../../../../components/ui-kit/icons/svg/MasterImg.svg'
import VisaImg from '../../../../components/ui-kit/icons/svg/VisaImg.svg'
import DiscoverImg from '../../../../components/ui-kit/icons/svg/DiscoverImg.svg'
import MaestroIcon from '../../../../components/ui-kit/icons/opImages/maestro.webp'
import subscriptionQuery from '../../../../reactQuery/subscriptionQuery'
import { usePortalStore } from '../../../../store/userPortalSlice'
import CancelTMFSubscription from '../../../../components/Subscription/CancelTMFSubscription'
import { useNavigate } from 'react-router-dom'
import { formatFeatureSettingsValue } from '../../../Subscription/helpers'
import { useSubscriptionStore } from '../../../../store/useSubscriptionStore'

const Subscriptions = ({ setValue }) => {
  const classes = useStyles()
  const navigate = useNavigate()
  const portalStore = usePortalStore((state) => state)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)

  const { data: subscriptionsData, isLoading, isError, error, refetch } =
    subscriptionQuery.getPlayerSubscriptionDataQuery({
      successToggler: (data) => console.log('### SubscriptionsData', data),
      errorToggler: (err) => console.log('### SubscriptionsError', err)
    })

  const handleCancelSubscription = (id) => {
    portalStore.openPortal(
      () => <CancelTMFSubscription subscriptionId={id} refetchSubscriptions={refetch} />,
      'innerModal'
    )
  }

  if (isLoading) {
    return (
      <Box className='space-y-3'>
        <Skeleton variant='text' width={180} height={30} />
        <Skeleton variant='rectangular' width='100%' height={60} />
        <Skeleton variant='rectangular' width='100%' height={60} />
      </Box>
    )
  }
  if (isError) return <Typography color='error'>{error?.message || 'Something went wrong'}</Typography>

  const subscriptionDetail = userSubscription?.subscriptionDetail
  const rows = subscriptionsData?.data?.rows || []
  // const currentPlan = rows.find(plan => plan.status === 'active' || (plan.status === 'cancelled' && plan.endDate > new Date())) || null
  const currentPlan = rows.find(plan => plan.subscriptionId === subscriptionDetail?.subscriptionId) || null
  const planHistory = rows.filter(plan => plan !== currentPlan)
  const end = new Date(currentPlan?.endDate)
  const daysRemaining = Math.max(0, Math.ceil((end - new Date()) / (1000 * 60 * 60 * 24)))

  return (
    <Box className={classes.subscriptionsContainer}>
      {/* PLAN DETAILS */}
      <Box className='subscriptions-card'>
        <TableContainer component={Paper}>
          <Table className='subscription-table'>
            <TableHead className='table-head'>
              <TableRow>
                <TableCell className='table-head-text'>Plan Details</TableCell>
                <TableCell className='table-head-text' align='center'>Date</TableCell>
                <TableCell className='table-head-text' align='center'>Amount</TableCell>
                <TableCell className='table-head-text' align='center'>Remaining Days</TableCell>
                <TableCell className='table-head-text' align='center'>Status</TableCell>
                <TableCell className='table-head-text' align='center'>Auto Renew</TableCell>
              </TableRow>
            </TableHead>

            <TableBody className='table-body'>
              {currentPlan && (
                <TableRow className='table-body-row'>
                  {/* PLAN NAME + FEATURES */}
                  <TableCell className='table-item-row'>
                    <Typography className='current-plan-sub-title'>
                      {currentPlan.subscriptionDetail?.name} Plan
                    </Typography>
                    {currentPlan.subscriptionDetail?.features?.map((f, idx) => (
                      <Typography
                        key={idx}
                        className='current-plan-description'
                        variant='body2'
                        color='white'
                      >
                        {f.featureDetail?.name}: {formatFeatureSettingsValue(f.featureDetail.name, f.featureValue)}
                      </Typography>
                    ))}
                  </TableCell>

                  {/* DATE RANGE */}
                  <TableCell className='table-item-row' align='center'>
                    <Typography color='white' sx={{ fontWeight: '600' }}>
                      {new Date(currentPlan.startDate).toLocaleDateString()} –{' '}
                      {new Date(currentPlan.endDate).toLocaleDateString()}
                    </Typography>
                  </TableCell>

                  {/* AMOUNT */}
                  <TableCell className='table-item-row' align='center'>
                    <Typography color='white' sx={{ fontWeight: '600' }}>
                      {currentPlan?.planType === 'monthly'
                        ? `$${currentPlan.subscriptionDetail?.monthlyAmount}`
                        : `$${currentPlan.subscriptionDetail?.yearlyAmount}`}
                    </Typography>
                  </TableCell>

                  {/* REMAINING DAYS */}
                  <TableCell className='table-item-row' align='center'>
                    <Typography color='white' sx={{ fontWeight: '600' }}>{daysRemaining}</Typography>
                  </TableCell>

                  {/* STATUS */}
                  <TableCell className='table-item-row' align='center'>
                    <Typography color='white' sx={{ fontWeight: '600' }}>{daysRemaining < 0 ? 'EXPIRED' : 'ACTIVE'}</Typography>
                  </TableCell>

                  {/* AUTO RENEW */}
                  <TableCell className='table-item-row' align='center'>
                    <Typography color='white' sx={{ fontWeight: '600' }}>
                      {currentPlan.autoRenew ? 'ACTIVE' : 'INACTIVE'}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {/* ACTION BUTTONS */}
        {currentPlan && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, p: 1 }}>
            {currentPlan?.isUpgradeAvailable && (
              <Button
                variant='outlined'
                className='btn btn-secondary'
                onClick={() => navigate('/subscriptions')}
              >
                Upgrade Plan
              </Button>
            )}
            {currentPlan?.autoRenew && (
              <Button
                variant='contained'
                className='btn btn-primary'
                onClick={() => handleCancelSubscription(currentPlan.id)}
              >
                Cancel Auto Renew
              </Button>
            )}
          </Box>
        )}
        {!currentPlan && (
          <Typography variant='h6' sx={{ color: 'red', textAlign: 'center', p: 2 }}>No active subscription found.</Typography>
        )}
      </Box>

      {/* PAYMENT METHOD */}
      <Box className='genral-tab'>
        <Typography variant='h6' sx={{ backgroundColor: '#1E1E1E', p: 1 }}>Payment Method</Typography>
        <Box className={classes.prefferdPaymentBox}>
          {currentPlan?.preferredPayment.length === 0 && (
            <Typography variant='h6' p={2} sx={{ color: 'red', textAlign: 'center' }}>
              No preferred payment methods found
            </Typography>
          )}
          {currentPlan?.preferredPayment.map((payment, key) => (
            <Box key={key} className='preferred-card'>
              <Box className='card-detail'>
                {payment?.moreDetails.cardType === 'VI' && <img src={VisaImg} alt='Visa' />}
                {payment?.moreDetails.cardType === 'DI' && <img src={DiscoverImg} alt='Discover' />}
                {payment?.moreDetails.cardType === 'MD' && <img src={MaestroIcon} alt='Maestro' />}
                {payment?.moreDetails.cardType === 'MC' && <img src={MasterImg} alt='MasterCard' />}
                {payment?.moreDetails.cardType === 'AM' && <img src={AmexImg} alt='Amex' />}
                <Box className='bank-details'>
                  <Box className='bank-name'>
                    <Typography>{payment?.moreDetails.holderName}</Typography>
                  </Box>
                  <Box className='bank-number'>
                    <Typography className='holder-name border-right'>**** {payment?.moreDetails.lastDigits}</Typography>
                    <Typography className='holder-name'>
                      {payment?.moreDetails.cardExpiry?.month}/{payment?.moreDetails.cardExpiry?.year}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      {/* PLAN HISTORY TABLE */}
      <Typography variant='h6'>Plan History</Typography>
      <Box className='subscriptions-card'>
        <TableContainer component={Paper} sx={{ backgroundColor: 'transparent', boxShadow: 'none' }}>
          <Table className='subscription-table'>
            <TableHead className='table-head'>
              <TableRow>
                <TableCell className='table-head-text'>Plan</TableCell>
                <TableCell className='table-head-text'>Features</TableCell>
                <TableCell className='table-head-text'>Date</TableCell>
                <TableCell className='table-head-text'>Amount</TableCell>
              </TableRow>
            </TableHead>

            <TableBody className='table-body'>
              {planHistory.length > 0
                ? (planHistory.map((plan) => (
                  <TableRow key={plan.id} className='table-body-row'>
                    <TableCell className='table-item-row'>{plan.subscriptionDetail?.name} Plan</TableCell>
                    <TableCell className='table-item-row'>
                      {plan.subscriptionDetail?.features?.map((f, idx) => (
                        <Typography key={idx} variant='body2' color='white'>
                          {f.featureDetail?.name}: {f.featureValue}
                        </Typography>
                      ))}
                    </TableCell>
                    <TableCell className='table-item-row'>
                      {new Date(plan.startDate).toLocaleDateString()} -{' '}
                      {new Date(plan.endDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell className='table-item-row'>
                      {plan?.planType === 'monthly' ? (`$${plan.subscriptionDetail?.monthlyAmount}`) : (`$${plan.subscriptionDetail?.yearlyAmount}`)}
                    </TableCell>
                  </TableRow>)))
                : (
                  <TableRow>
                    <TableCell colSpan={4} align='center'>
                      <Typography variant='h6' sx={{ color: 'red', textAlign: 'center' }}>
                        No previous plans found.
                      </Typography>
                    </TableCell>
                  </TableRow>)}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  )
}

export default Subscriptions
