import React, { useState } from 'react'
import { Box, Button, CircularProgress, Grid, Typography } from '@mui/material'
import AmexImg from '../../../../components/ui-kit/icons/svg/AmexImg.svg'
import MasterImg from '../../../../components/ui-kit/icons/svg/MasterImg.svg'
import VisaImg from '../../../../components/ui-kit/icons/svg/VisaImg.svg'
import DiscoverImg from '../../../../components/ui-kit/icons/svg/DiscoverImg.svg'
import skrill from '../../../../components/ui-kit/icons/svg/skrill.svg'
import Bank from '../../../../components/ui-kit/icons/svg/bank.svg'
import applePayIcon from '../../../../components/ui-kit/icons/svg/apple-pay.svg'
import MaestroIcon from '../../../../components/ui-kit/icons/opImages/maestro.webp'
import { getOSAndBrowser } from '../../../../utils/helpers'
import paymentQuery from '../../../../reactQuery/paymentQuery'
import toast from 'react-hot-toast'

const PreferredPayments = () => {
  const browserType = getOSAndBrowser()

  const [isDeleteLoading, setIsDeleteLoading] = useState(false)

  // GET ALL USER PREFERRED PAYMENT METHODS
  const { data: preferredPayment, isloading } = paymentQuery.getPrefferedPaymentQuery({})

  // DELETE USER PREFERRED PAYMENT METHODS
  const deletePaymentMethod = paymentQuery.deletePreferredPaymentMutation({
    onSuccess: (res) => {
      setIsDeleteLoading(false)
      toast.success('Preferred Payment Removed Successfully')
    },
    onError: (err) => {
      console.log('PaymentDeleteError', err)
      setIsDeleteLoading(false)
      toast.error('Preferred Payment Remove Failed')
    }
  })

  const onDeleteMethod = (paymentMethodName) => {
    setIsDeleteLoading(true)
    deletePaymentMethod.mutate({ paymentMethodName: paymentMethodName })
  }

  const renderPaymentCard = (payment, index) => {
    const { paymentMethodType, paymentMethodName, moreDetails = {} } = payment
    if (paymentMethodType === 'CARD') {
      return (
        <Box key={index} className='preferred-card'>
          <Box className='card-detail'>
            {moreDetails.cardType === 'VI' && <img src={VisaImg} alt='Visa' />}
            {moreDetails.cardType === 'DI' && <img src={DiscoverImg} alt='Discover' />}
            {moreDetails.cardType === 'MD' && <img src={MaestroIcon} alt='Maestro' />}
            {moreDetails.cardType === 'MC' && <img src={MasterImg} alt='MasterCard' />}
            {moreDetails.cardType === 'AM' && <img src={AmexImg} alt='Amex' />}
            <Box className='bank-details'>
              <Box className='bank-name'>
                <Typography>{moreDetails.holderName}</Typography>
              </Box>
              <Box className='bank-number'>
                <Typography className='holder-name border-right'>**** {moreDetails.lastDigits}</Typography>
                <Typography className='holder-name'>
                  {moreDetails.cardExpiry?.month}/{moreDetails.cardExpiry?.year}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box className='card-remove'>
            <Button variant='text' className='remove-button' onClick={() => onDeleteMethod(paymentMethodName)}>
              Remove
            </Button>
          </Box>
        </Box>
      )
    }

    if (paymentMethodType === 'PAY_BY_BANK') {
      return (
        <Box key={index} className='preferred-card'>
          <Box className='card-detail'>
            <img src={Bank} alt='Bank' />
            <Box className='bank-details'>
              <Box className='bank-name'>
                <Typography>{moreDetails.bankName}</Typography>
              </Box>
              <Box className='bank-number'>
                <Typography className='holder-name'>**** **{moreDetails.lastDigits}</Typography>
              </Box>
            </Box>
          </Box>
          <Box className='card-remove'>
            <Button variant='text' className='remove-button' onClick={() => onDeleteMethod(paymentMethodName)}>
              Remove
            </Button>
          </Box>
        </Box>
      )
    }

    if (paymentMethodType === 'TRUSTLY') {
      return (
        <Box key={index} className='preferred-card'>
          <Box className='card-detail'>
            <img
              src={`https://paywithmybank.com/start/assets/institutions/icons/${moreDetails.bankIdentifier}.png`}
              alt='Trustly Bank Logo'
              style={{ width: '30px', height: 'inherit' }}
            />
            <Box className='bank-details'>
              <Box className='bank-name'>
                <Typography>{moreDetails.bankName}</Typography>
              </Box>
              <Box className='bank-number'>
                <Typography className='holder-name'>
                  {moreDetails.accountType?.toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase())} | ****
                  {moreDetails.accountNumber}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box className='card-remove'>
            <Button variant='text' className='remove-button' onClick={() => onDeleteMethod(paymentMethodName)}>
              Remove
            </Button>
          </Box>
        </Box>
      )
    }

    if (paymentMethodType === 'SKRILL') {
      return (
        <Box key={index} className='preferred-card'>
          <Box className='card-detail'>
            <img src={skrill} alt='Skrill' />
            <Box className='bank-details'>
              <Box className='bank-name'>
                <Typography>{payment.paymentMethodName}</Typography>
              </Box>
            </Box>
          </Box>
          <Box className='card-remove'>
            <Button variant='text' className='remove-button' onClick={() => onDeleteMethod(paymentMethodName)}>
              Remove
            </Button>
          </Box>
        </Box>
      )
    }

    if (paymentMethodType === 'APPLE_PAY' && (browserType.os === 'MacOS' || browserType.os === 'iOS')) {
      return (
        <Box key={index} className='preferred-card'>
          <Box className='card-detail'>
            <img src={applePayIcon} alt='Apple Pay' />
            <Box className='bank-details'>
              <Box className='bank-name'>
                <Typography>Apple Pay</Typography>
              </Box>
            </Box>
          </Box>
          <Box className='card-remove'>
            <Button variant='text' className='remove-button' onClick={() => onDeleteMethod(paymentMethodName)}>
              Remove
            </Button>
          </Box>
        </Box>
      )
    }
  }

  // Filter Apple Pay if OS is not supported
  const filteredPreferredPayment = (preferredPayment ?? []).filter((payment) => {
    if (payment.paymentMethodType === 'APPLE_PAY') {
      return browserType.os === 'MacOS' || browserType.os === 'iOS'
    }
    return true
  })

  return (
    <Box className='genral-tab'>
      <Grid className='setting-card-header'>
        <Typography variant='h4'>Manage Preferred Payments</Typography>
      </Grid>
      <Grid className='setting-card-details'>
        <Box className='preferred-box'>
          {isloading || isDeleteLoading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '160px'
              }}
            >
              <CircularProgress />
              <Typography>Loading payment methods...</Typography>
            </Box>
          ) : filteredPreferredPayment.length === 0 ? (
            <Typography sx={{ textAlign: 'center', padding: '10px', marginBottom: '0', color: 'red' }}>
              No preferred payment methods found
            </Typography>
          ) : (
            <Box>{filteredPreferredPayment.map(renderPaymentCard)}</Box>
          )}
        </Box>
      </Grid>
    </Box>
  )
}

export default PreferredPayments
