import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    background: '#000000 !important'
  },

  /* HERO HEADER */
  headerWrapper: {
    position: 'relative',
    overflow: 'hidden',
    textAlign: 'center',
    padding: '1rem 1rem 1rem',
    background: 'linear-gradient(135deg, #1B181E, #0F0D11)'
  },
  headerOverlay: {
    position: 'absolute',
    inset: 0,
    background: 'rgba(0,0,0,0.4)'
  },
  headerContent: {
    position: 'relative',
    zIndex: 2
  },
  headerTitle: {
    fontSize: '3rem',
    fontWeight: '700',
    margin: 0,
    color: '#fff',
    lineHeight: 1.2,
    [theme.breakpoints.down('md')]: {
      fontSize: '2rem'
    }
  },
  headerAccent: {
    display: 'block',
    color: '#FDB72E'
  },
  promoBadge: {
    marginTop: '1rem',
    background: '#FDB72E',
    color: '#000',
    display: 'inline-block',
    padding: '0.5rem 1.5rem',
    fontWeight: 700,
    borderRadius: '50px',
    fontSize: '1rem',
    animation: '$pulse 1.5s infinite ease-in-out'
  },

  /* TOGGLE */
  toggleContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '0.5rem',
    margin: '1rem 0 0 0',
    fontSize: '1.2rem',
    color: '#ccc'
  },
  toggleLabel: {
    fontSize: '1.5rem !important',
    fontWeight: '700 !important',
    color: '#9ca3af' // gray by default
  },
  toggleLabelActive: {
    fontSize: '1.5rem !important',
    fontWeight: '700 !important',
    color: theme.colors.YellowishOrange // orange when active
  },
  planSwitch: {
    width: '70px !important',
    height: '42px !important',
    padding: '0 !important',
    borderRadius: 20,
    border: `2px solid ${theme.colors.YellowishOrange}`,
    '& .MuiSwitch-switchBase': {
      padding: 0,
      margin: 2,
      color: '#fff',
      '&.Mui-checked': {
        transform: 'translateX(30px)',
        color: '#fff',
        '& + .MuiSwitch-track': {
          backgroundColor: theme.colors.YellowishOrange, // full fill
          opacity: 1,
          border: `2px solid ${theme.colors.YellowishOrange}` // keep border aligned
        }
      }
    },
    '& .MuiSwitch-thumb': {
      width: 33,
      height: 33,
      backgroundColor: '#fff',
      boxShadow: '0 2px 5px rgba(0,0,0,0.3)'
    },
    '& .MuiSwitch-track': {
      borderRadius: 0,
      backgroundColor: '#3a3f4b', // dark by default
      opacity: 1,
      transition: theme.transitions.create(['background-color'], {
        duration: 400
      })
    }
  },

  /* PLAN CARDS */
  desktopPlans: {
    display: 'flex',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: '2rem',
    padding: '2rem'
  },
  card: {
    width: 350,
    background: '#151515',
    border: `2px solid ${theme.colors.YellowishOrange}`,
    borderRadius: 12,
    padding: '1.5rem',
    position: 'relative',
    boxShadow: '0 0 15px rgba(0,0,0,0.4)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      transform: 'translateY(-10px)',
      boxShadow: '0 0 25px rgba(253,183,46,0.6)'
    },
    [theme.breakpoints.down('sm')]: {
      width: '100%'
    }
  },
  disabledCard: {
    width: 320,
    background: '#151515',
    border: '2px solid #7b7777ff',
    borderRadius: 12,
    padding: '1.5rem',
    position: 'relative',
    boxShadow: '0 0 15px rgba(0,0,0,0.4)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      boxShadow: '0 0 25px #7b7777ff'
    }
  },
  thumbnailBox: {
    width: theme.spacing(4),
    minWidth: theme.spacing(4),
    height: theme.spacing(4),
    borderRadius: '50%',
    overflow: 'hidden',
    zIndex: 2,
    '& img': {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
      objectPosition: 'center',
      zIndex: 3
    }
  },
  planName: {
    textAlign: 'center',
    color: '#FDB72E',
    fontSize: '1.5rem',
    marginBottom: '0.3rem'
  },
  description: {
    textAlign: 'center',
    color: '#999',
    fontSize: '1.2rem',
    fontWeight: 'bold'
  },
  price: {
    textAlign: 'center',
    color: '#fff',
    fontSize: '2.2rem',
    margin: 0
  },
  priceNote: {
    fontSize: '1rem',
    color: '#ccc'
  },
  coins: {
    textAlign: 'center',
    color: '#FDB72E',
    fontWeight: 'bold',
    margin: '5px 0'
  },
  duration: {
    textAlign: 'center',
    color: '#999',
    fontSize: '0.9rem'
  },

  /* FEATURES */
  featureList: {
    listStyle: 'none',
    padding: 0,
    marginTop: '1rem',
    width: '100%'
  },
  featureItem: {
    color: '#ddd',
    marginBottom: '0.6rem',
    display: 'flex',
    alignItems: 'center'
  },
  featureValue: {
    marginLeft: 5,
    marginRight: 5,
    color: '#FFD700'
  },

  /* CTA */
  ctaButton: {
    marginTop: '1rem',
    width: '100%',
    padding: '0.8rem',
    background: '#FDB72E',
    color: '#000',
    fontWeight: 'bold',
    fontSize: '1rem',
    border: 'none',
    borderRadius: 8,
    cursor: 'pointer',
    '&:hover': {
      background: '#FFC94D'
    }
  },
  footerText: {
    textAlign: 'center',
    color: '#777',
    fontWeight: '600',
    fontSize: '0.8rem',
    marginTop: 8
  },

  /* MOBILE */
  mobileContainer: {
    marginTop: '2rem',
    padding: '1rem',
    display: 'none',
    [theme.breakpoints.down('md')]: {
      display: 'block'
    }
  },

  mobileTabs: {
    marginBottom: '1rem'
  },

  tabRoot: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: theme.shape.borderRadius,
    minWidth: 0
  },
  active: {
    backgroundColor: theme.colors.YellowishOrange,
    color: theme.colors.textBlack,
    boxShadow: theme.shadows[4],
    transform: 'scale(1.05)',
    padding: '0 !important'
  },
  popular: {
    color: theme.palette.grey[300],
    border: '1px solid rgba(255, 255, 255, 0.3)',
    boxShadow: theme.shadows[2],
    '&:hover': {
      color: '#fff',
      transform: 'scale(1.05)'
    }
  },
  default: {
    color: theme.palette.grey[400],
    border: '1px solid transparent',
    '&:hover': {
      color: '#fff',
      transform: 'scale(1.05)'
    }
  },
  tabContent: {
    width: '100%',
    padding: '1rem',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'grey'
  },
  tabContentActive: {
    width: '100%',
    padding: '1rem',
    borderRadius: '10px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.YellowishOrange,
    color: theme.colors.textBlack
  },
  thumbnail: {
    display: 'flex',
    justifyContent: 'center',
    width: theme.spacing(4),
    minWidth: theme.spacing(4),
    height: theme.spacing(4),
    borderRadius: '50%',
    overflow: 'hidden',
    zIndex: 2,
    '& img': {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
      objectPosition: 'center',
      zIndex: 3
    }
  },
  name: {
    fontSize: '1rem !important',
    fontWeight: '700 !important'
  },

  singleCard: {
    display: 'flex',
    justifyContent: 'center'
  },

  /* SOCIAL PROOF */
  socialProofSection: {
    textAlign: 'center',
    padding: '4rem 1rem',
    background: '#111',
    marginTop: '3rem'
  },
  testimonialCard: {
    background: '#1A1A1A',
    border: '1px solid rgba(253,183,46,0.3)',
    padding: '1.5rem',
    borderRadius: 12,
    minHeight: 160
  },
  star: {
    color: '#FDB72E'
  },

  /* LIVE WINNERS */
  liveWinners: {
    background: '#132B27',
    padding: '1rem',
    marginTop: '2rem',
    overflow: 'hidden',
    borderTop: '1px solid rgba(255,255,255,0.1)',
    borderBottom: '1px solid rgba(255,255,255,0.1)'
  },
  winnerTicker: {
    display: 'flex',
    gap: 32,
    animation: '$scroll 45s linear infinite',
    '&:hover': {
      animationPlayState: 'paused'
    }
  },
  winnerItem: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    whiteSpace: 'nowrap',
    padding: '6px 12px',
    background: 'rgba(255,255,255,0.05)',
    borderRadius: 20
  },
  winnerIcon: {
    color: '#FDB72E'
  },

  /* FAQ */
  faqSection: {
    background: '#111',
    padding: '3rem 1rem',
    marginTop: '3rem'
  },
  faqTitle: {
    textAlign: 'center',
    fontWeight: '700',
    fontSize: '2rem',
    color: '#FDB72E',
    marginBottom: '1.5rem'
  },
  accordion: {
    background: '#1A1A1A !important',
    color: '#fff',
    marginBottom: '0.5rem',
    border: '1px solid rgba(255,255,255,0.1)'
  },
  popularBadge: {
    fontSize: '0.75rem !important',
    position: 'absolute',
    bottom: -20,
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: '#FDB72E',
    color: '#000',
    fontWeight: '700 !important',
    borderRadius: '9999px',
    display: 'flex',
    alignItems: 'center',
    gap: 0.5,
    boxShadow: '0px 2px 4px rgba(0,0,0,0.2)'
  },

  /* Animations */
  '@keyframes pulse': {
    '0%': { transform: 'scale(1)', opacity: 1 },
    '50%': { transform: 'scale(1.02)', opacity: 0.8 },
    '100%': { transform: 'scale(1)', opacity: 1 }
  },
  '@keyframes scroll': {
    '0%': { transform: 'translateX(0)' },
    '100%': { transform: 'translateX(-50%)' }
  }
}))
