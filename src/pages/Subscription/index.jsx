import React, { useEffect, useState } from 'react'
import {
  Box,
  CircularProgress,
  Switch,
  Tab,
  Tabs,
  Typography
} from '@mui/material'
import subscriptionQuery from '../../reactQuery/subscriptionQuery'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { usePortalStore } from '../../store/store'
import PaymentStatus from '../../components/PaymentStatus'
import StepperForm from '../../components/StepperForm'
import { useLocation } from 'react-router-dom'
import useStyles from './Subscription.styles'
import useGetDeviceType from '../../utils/useGetDeviceType'
import SubscriptionFaq from './components/SubscriptionFaq'
import SubscriptionCard from './components/SubscriptionCard'
import { subscriptionsSocket } from '../../utils/socket'
import FooterCTA from './components/FooterCTA'
import SocialProof from './components/SocialProof'
import ValueUrgencySections from './components/ValueUrgencySections'
import LiveWinners from './components/LiveWinners'
import LiveSubscriber from './components/LiveSubscriber'
import HighestPlan from './components/HighestPlan'
import TrustBadges from './components/TrustBadges'
import LazyImage from '../../utils/lazyImage'

const Subscriptions = () => {
  const { isMobile } = useGetDeviceType()
  const classes = useStyles()
  const location = useLocation()
  const portalStore = usePortalStore(state => state)

  const {
    userSubscription,
    setUserSubscription,
    yearly,
    setYearly,
    setSelectedPlan,
    setIsUpgrade,
    setRefundAmount
  } = useSubscriptionStore(state => state)

  const [loading, setLoading] = useState(true)
  const [subscriptionsData, setSubscriptionsData] = useState([])
  const [isHighestPlan, setIsHighestPlan] = useState(false)
  const [snackOpen, setSnackOpen] = useState(false)
  const [snackMsg, setSnackMsg] = useState('')

  const [selectedTab, setSelectedTab] = useState(0)

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue)
  }

  const { mutate: getSubscriptionStatus } =
    subscriptionQuery.getSubscriptionMutation({
      onSuccess: res => {
        setUserSubscription(res?.data?.data)
        refetchSubscriptions()
      },
      onError: error => console.log('Error getting profile:', error)
    })

  const { data, refetch: refetchSubscriptions } =
    subscriptionQuery.getSubscriptionDataQuery({
      successToggler: res => {
        if (res?.success === false) {
          setSubscriptionsData([])
          setIsHighestPlan(true)
          setLoading(false)
        } else {
          if (res.currentSubscriptionPlan) {
            setSubscriptionsData([res.currentSubscriptionPlan, ...res.data])
          } else {
            setSubscriptionsData(res.data)
          }
          setIsUpgrade(res.upgradeSubscription)
          setLoading(false)
        }
      },
      errorToggler: err => {
        console.log('Error getting subscriptions:', err)
        setLoading(false)
      }
    })

  const handleBuyNow = (plan) => {
    if (!plan) return
    setSelectedPlan(plan)
    if (yearly) {
      setRefundAmount(plan?.adjustedYearlyAmount)
    } else {
      setRefundAmount(plan?.adjustedMonthlyAmount)
    }

    portalStore.openPortal(
      () => (
        <Box className='stepper-outer-box'>
          <StepperForm
            stepperCalledFor='subscription_purchase'
            packageDetails={plan}
          />
        </Box>
      ),
      'StepperModal'
    )
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')
    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: statuss,
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'paymentmodal')
      getSubscriptionStatus({ currentSubscriptionDetail: true })
    }
    refetchSubscriptions()
  }, [location])

  // Subscription Socket
  useEffect(() => {
    subscriptionsSocket.connect()
    return () => {
      subscriptionsSocket.disconnect()
    }
  }, [])

  const handleSnackClose = (event, reason) => {
    if (reason === 'clickaway') {
      return
    }
    setSnackMsg('')
    setSnackOpen(false)
  }

  useEffect(() => {
    if (!subscriptionsSocket) return
    subscriptionsSocket.on('SUBSCRIPTION_LIVE_ACTIVITY', (data) => {
      console.log('####SUBSCRIPTION_LIVE_ACTIVITY', data.data.socketMessage)
      setSnackMsg(data.data.socketMessage)
      setSnackOpen(true)
    })
  }, [subscriptionsSocket])

  const renderSubscriptionCards = () => {
    const uniquePlans = subscriptionsData
      ?.filter((plan, index, self) =>
        index === self.findIndex(p => p.subscriptionId === plan.subscriptionId)
      )
      .slice() // avoid mutating
      .sort((a, b) => yearly
        ? a.yearlyAmount - b.yearlyAmount
        : a.monthlyAmount - b.monthlyAmount
      )

    return uniquePlans.map((plan) => {
      const monthlyPrice = plan?.monthlyAmount
      const yearlyPrice = plan?.yearlyAmount
      const dailyPrice = yearly
        ? ((yearlyPrice / 12) / 30).toFixed(2)
        : (monthlyPrice / 30).toFixed(2)
      const priceToShow = yearly ? yearlyPrice / 12 : monthlyPrice

      const isCurrentPlan =
        plan.subscriptionId === data?.currentSubscriptionPlan?.subscriptionId
      // &&
      // (
      //   (yearly && data?.currentSubscriptionPlan?.currentPlanType === 'yearly') ||
      //   (!yearly && data?.currentSubscriptionPlan?.currentPlanType === 'monthly')
      // )

      return (
        <SubscriptionCard
          key={plan.subscriptionId}
          plan={plan}
          priceToShow={priceToShow}
          monthlyPrice={monthlyPrice}
          yearlyPrice={yearlyPrice}
          dailyPrice={dailyPrice}
          yearly={yearly}
          handleBuyNow={handleBuyNow}
          isCurrentPlan={isCurrentPlan}
          currentPlanType={userSubscription?.userSubscriptionDetail?.planType}
          isUpgrade={data.upgradeSubscription}
        />
      )
    })
  }

  return (
    <div className={classes.lobbyRight}>
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', padding: 10 }}>
          <CircularProgress />
        </Box>
      )}
      {subscriptionsData?.length === 0 && !isHighestPlan && (
        <Box sx={{ display: 'flex', justifyContent: 'center', padding: 10 }}>
          <Typography color='white' variant='h4'>No TMF+ plans available at this moment.</Typography>
        </Box>
      )}
      {isHighestPlan
        ? (<HighestPlan />)
        : (
          <>
            {!loading && subscriptionsData?.length > 0 && (
              <>
                <div className={classes.headerWrapper}>
                  <div className={classes.headerOverlay} />
                  <div className={classes.headerContent}>
                    <h1 className={classes.headerTitle}>
                      Choose Your
                      <span className={classes.headerAccent}>Gaming Destiny</span>
                    </h1>
                    <div className={classes.promoBadge}>
                      🔥 LIMITED TIME: Save 25% on Annual Plans!
                    </div>
                  </div>
                </div>

                {/* Toggle Monthly / Yearly */}
                <div className={classes.toggleContainer}>
                  <Typography
                    variant='body1'
                    className={!yearly ? classes.toggleLabelActive : classes.toggleLabel}
                  >
                    Monthly
                  </Typography>

                  <Switch
                    checked={yearly}
                    onChange={() => setYearly(!yearly)}
                    className={classes.planSwitch}
                  />

                  <Typography
                    variant='body1'
                    className={yearly ? classes.toggleLabelActive : classes.toggleLabel}
                  >
                    Yearly
                  </Typography>
                </div>
              </>
            )}
            {!isMobile
              ? (<div className={classes.desktopPlans}>{renderSubscriptionCards()}</div>)
              : (
                <div className={classes.mobileContainer}>
                  <Tabs
                    value={selectedTab}
                    onChange={handleTabChange}
                    variant='scrollable'
                    indicatorColor='yellow'
                    scrollButtons={false}
                    allowScrollButtonsMobile={false}
                    className={classes.mobileTabs}
                    centered
                  >
                    {subscriptionsData.map((plan, index) => {
                      const isActive = selectedTab === index

                      let tabClass = classes.tabRoot
                      if (isActive) {
                        tabClass += ` ${classes.active}`
                      } else if (plan.popular) {
                        tabClass += ` ${classes.popular}`
                      } else {
                        tabClass += ` ${classes.default}`
                      }

                      return (
                        <Tab
                          key={plan.subscriptionId}
                          value={index}
                          className={tabClass}
                          disableRipple
                          label={
                            <Box className={isActive ? classes.tabContentActive : classes.tabContent}>
                              <Box className={classes.thumbnail}>
                                <LazyImage
                                  src={plan?.thumbnail}
                                  alt='Plan Thumbnail'
                                  aspectRatio='1/1'
                                  borderRadius='50%'
                                  sizes='40px'
                                  priority
                                />
                              </Box>
                              <span className={classes.name}>{plan.name.split(' ')[0]}</span>
                            </Box>
                          }
                        />
                      )
                    })}
                  </Tabs>

                  <Box className={classes.singleCard}>
                    {
                      renderSubscriptionCards()[selectedTab]
                    }
                  </Box>
                </div>)}
          </>)}
      <SocialProof />
      <LiveWinners />
      <TrustBadges />
      <ValueUrgencySections />
      <Box className={classes.faqSection}>
        <SubscriptionFaq />
      </Box>
      <FooterCTA />
      {snackMsg !== '' && (
        <LiveSubscriber snackOpen={snackOpen} handleSnackClose={handleSnackClose} dataMsg={snackMsg} />
      )}
    </div>
  )
}

export default Subscriptions
