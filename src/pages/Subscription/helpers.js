import AccountBalanceWalletTwoToneIcon from '@mui/icons-material/AccountBalanceWalletTwoTone'
import AssuredWorkloadTwoToneIcon from '@mui/icons-material/AssuredWorkloadTwoTone'
import EmojiEventsTwoToneIcon from '@mui/icons-material/EmojiEventsTwoTone'
import EmojiFlagsTwoToneIcon from '@mui/icons-material/EmojiFlagsTwoTone'
import HistoryTwoToneIcon from '@mui/icons-material/HistoryTwoTone'
import Inventory2TwoToneIcon from '@mui/icons-material/Inventory2TwoTone'
import OfflineBoltTwoToneIcon from '@mui/icons-material/OfflineBoltTwoTone'
import ShoppingBagIcon from '@mui/icons-material/ShoppingBag'

export const formatFeatureValue = (name, value) => {
  switch (name) {
    case 'Daily Bonus Multiplier':
      return `${value}X`
    case 'Tournament Joining Fee Discount':
      return `${value}%`
    case 'Vault Interest Rate':
      return `${value}%`
    case 'Subscriber Only Package':
      return null
    case 'Exclusive Package Discount':
      return `${value}%`
    case 'Weekly Free Spin':
      return value
    case 'Subscriber Only Tournament':
      return null
    case 'Guaranteed Redemption Approved Time for Subscribers':
      return null
    default:
      return value
  }
}

export const formatFeatureSettingsValue = (name, value) => {
  switch (name) {
    case 'Daily Bonus Multiplier':
      return `${value}X`
    case 'Tournament Joining Fee Discount':
      return `${value}%`
    case 'Vault Interest Rate':
      return `${value}%`
    case 'Subscriber Only Package':
      return value
    case 'Exclusive Package Discount':
      return `${value}%`
    case 'Weekly Free Spin':
      return value
    case 'Subscriber Only Tournament':
      return value
    case 'Guaranteed Redemption Approved Time for Subscribers':
      return `${value} Hours`
    default:
      return value
  }
}

export const formatFeatureIcon = (name) => {
  switch (name) {
    case 'Daily Bonus Multiplier':
      return OfflineBoltTwoToneIcon
    case 'Tournament Joining Fee Discount':
      return EmojiEventsTwoToneIcon
    case 'Vault Interest Rate':
      return AccountBalanceWalletTwoToneIcon
    case 'Subscriber Only Package':
      return ShoppingBagIcon
    case 'Exclusive Package Discount':
      return Inventory2TwoToneIcon
    case 'Weekly Free Spin':
      return HistoryTwoToneIcon
    case 'Subscriber Only Tournament':
      return EmojiFlagsTwoToneIcon
    case 'Guaranteed Redemption Approved Time for Subscribers':
      return AssuredWorkloadTwoToneIcon
    default:
      return null
  }
}
