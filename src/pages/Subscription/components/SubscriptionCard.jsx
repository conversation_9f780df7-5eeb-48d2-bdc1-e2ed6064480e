import React from 'react'
import { <PERSON>, But<PERSON>, Chip, Divider } from '@mui/material'
import useStyles from '../Subscription.styles'
import RedeemTwoToneIcon from '@mui/icons-material/RedeemTwoTone'
import { formatFeatureIcon, formatFeatureValue } from '../helpers'
import LazyImage from '../../../utils/lazyImage'
import { BrandLogoMob } from '../../../components/ui-kit/icons/brand'
import FeatureIcon from './FeatureIcon'

const SubscriptionCard = ({ plan, priceToShow, monthlyPrice, yearlyPrice, dailyPrice, yearly, handleBuyNow, isCurrentPlan, isUpgrade, currentPlanType }) => {
  const classes = useStyles()

  const comparisons = [
    { text: 'soda', price: 2 },
    { text: 'cup of coffee', price: 3 },
    { text: 'slice of pizza', price: 4 },
    { text: 'bus fare', price: 2.5 },
    { text: 'movie rental', price: 5 },
    { text: 'fries', price: 2.5 },
    { text: 'ice cream cone', price: 3 },
    { text: 'donut', price: 1.5 },
    { text: 'sandwich', price: 6 },
    { text: 'energy drink', price: 4 },
    { text: 'pack of gum', price: 1.5 },
    { text: 'bottle of water', price: 2 },
    { text: 'burger', price: 5 },
    { text: 'salad', price: 7 },
    { text: 'subway ride', price: 2.75 },
    { text: 'chocolate bar', price: 2 },
    { text: 'cupcake', price: 3.5 },
    { text: 'streaming rental', price: 6 },
    { text: 'lottery ticket', price: 2 }
  ]

  const cheaperThan = comparisons.find(item => dailyPrice < item.price)

  return (
    <div key={plan.subscriptionId} className={isCurrentPlan ? classes.disabledCard : classes.card}>
      {!isCurrentPlan && plan?.weeklyPurchaseCount !== 0 && (
        <Chip
          label={`~ ${plan?.weeklyPurchaseCount} chose this week`}
          size='medium'
          sx={{
            position: 'absolute',
            top: -15,
            right: 8,
            background: 'linear-gradient(90deg, #00e676 0%, #00c853 100%)',
            color: '#fff',
            fontWeight: '800',
            fontSize: '0.75rem',
            borderRadius: '16px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            '& .MuiChip-label': {
              paddingLeft: '8px',
              paddingRight: '8px'
            }
          }}
        />
      )}

      <Box className={classes.thumbnailBox}>
        <LazyImage
          src={plan?.thumbnail || BrandLogoMob}
          alt='Plan Thumbnail'
          aspectRatio='1/1'
          borderRadius='50%'
          sizes='40px'
          priority
        />
      </Box>
      {isCurrentPlan ? (<h2 className={classes.planName}>CURRENT PLAN</h2>) : (<h2 className={classes.planName}>{(plan.name).toUpperCase()}</h2>)}
      <p className={classes.description}>{plan.description}</p>
      {!isCurrentPlan
        ? (
          <h1 className={classes.price}>
            ${priceToShow.toFixed(2)}{' '}
            <span className={classes.priceNote}>/ month</span>
          </h1>)
        : (
          <h1 className={classes.price}>
            ${currentPlanType === 'monthly' ? monthlyPrice.toFixed(2) : yearlyPrice.toFixed(2)}{' '}
            <span className={classes.priceNote}>{currentPlanType === 'monthly' ? '/ month' : '/ year'}</span>
          </h1>)}

      {!isCurrentPlan && (
        <p className={classes.coins}>
          Only ${dailyPrice}/ day
          <span className={classes.duration}> (less than a {cheaperThan?.text})</span>
        </p>
      )}

      {!isCurrentPlan && yearly && (
        <p className={classes.coins}>
          <span className={classes.duration}>${monthlyPrice}/ month </span>
          Save ${(monthlyPrice - (yearlyPrice / 12)).toFixed(2)}
        </p>
      )}

      {!isCurrentPlan && yearly && (
        <p className={classes.duration}>{plan.durationDays}Billed ${(yearlyPrice).toFixed(2)} annually • You save ${((monthlyPrice * 12) - (yearlyPrice)).toFixed(2)} per year</p>
      )}

      <Divider style={{ margin: '12px 0', background: '#333' }} />

      <ul className={classes.featureList}>
        <li className={classes.featureItem}>
          <RedeemTwoToneIcon style={{ color: '#FDB72E', fontSize: 16, marginRight: 5 }} />
          {plan.gcCoin.toLocaleString()} Gold Coins + {plan.scCoin.toLocaleString()} Sweepstakes Coins
        </li>
        {plan.features.map((feature, idx) => (
          <li key={idx} className={classes.featureItem}>
            <FeatureIcon icon={formatFeatureIcon(feature.featureDetail.name)} />
            <span className={classes.featureValue}>{formatFeatureValue(feature.featureDetail.name, feature.featureValue)} </span>
            {feature.featureDetail.name}
          </li>
        ))}
      </ul>

      {!isCurrentPlan && (
        <Button
          className='btn btn-primary'
          disabled={isCurrentPlan}
          onClick={() => handleBuyNow(plan)}
        >
          {plan?.specialPlan && (
            <span role='img' aria-label='rocket'>🚀  </span>
          )}
          {isUpgrade ? 'UPGRADE TO ' : 'START '}
          {plan.name.toUpperCase()}
        </Button>
      )}

      <p className={classes.footerText}>✅ Cancel anytime • 🔒 Secure payment</p>
      {plan?.specialPlan && (
        <Box
          className={classes.popularBadge}
          sx={{
            px: 1,
            py: 0.5
          }}
        >
          <span role='img' aria-label='fire'>🔥</span>
          MOST POPULAR
        </Box>
      )}
    </div>
  )
}

export default SubscriptionCard
