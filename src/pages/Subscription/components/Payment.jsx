import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Button, Grid, Box, Typography, TextField, FormControlLabel } from '@mui/material'
import PropTypes from 'prop-types'
import TagManager from 'react-gtm-module'
import useStyles from '../../Lobby/components/UsernamePopup/Username.styles'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'
import { CardImg } from '../../../components/ui-kit/icons/svg'
import AmexImg from '../../../components/ui-kit/icons/svg/AmexImg.svg'
import MasterImg from '../../../components/ui-kit/icons/svg/MasterImg.svg'
import VisaImg from '../../../components/ui-kit/icons/svg/VisaImg.svg'
import DiscoverImg from '../../../components/ui-kit/icons/svg/DiscoverImg.svg'
import skrill from '../../../components/ui-kit/icons/svg/skrill.svg'
import applePayIcon from '../../../components/ui-kit/icons/svg/apple-pay.svg'
import Bank from '../../../components/ui-kit/icons/svg/bank.svg'
import MaestroIcon from '../../../components/ui-kit/icons/opImages/maestro.webp'
import { usePortalStore, useUserStore, useStateStore, useDepositInitStore, usePaymentProcessStore } from '../../../store/store'
import useStepperStore from '../../../store/useStepperStore'
import { getOSAndBrowser } from '../../../utils/helpers'
import useSeon from '../../../utils/useSeon'
import usePaymentStyles from '../../Store/PaymentModal/payment.style'
import GeocomplyPopup from '../../../Context/GeocomplyPopup'
import VerifyModal from '../../Store/VerifyModal/Verify'
import { setupPaysafeFields } from '../../Store/PaymentModal/utils/paysafe/paysafeSetup'
import { handleCardPayment } from '../../Store/PaymentModal/utils/paysafe/paysafePaymentProcessors'
import { PaymentQuery } from '../../../reactQuery'
import PreferredPaymentCard from '../../Store/PaymentModal/components/PreferredPaymentCard'
import CustomSwitch from '../../../components/CustomSwitch'
import PreferredPaymentBox from '../../Store/PaymentModal/components/PreferredPaymentBox'
import BtnLoader from '../../Store/PaymentModal/components/BtnLoader'
import DepositInprogressModal from '../../Store/PaymentModal/components/DepositInprogressModal'
import subscriptionQuery from '../../../reactQuery/subscriptionQuery'
import PaymentLoader from '../../../components/Loader/PaymentLoader'
import PurchaseSummaryAccordion from './PaymentSummaryAccordion'
import PaymentTimer from '../../Store/PaymentModal/PaymentTimer'

function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <>{children}</>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

// Payment Methods Form Component
const PaymentMethodForm = ({ id }) => {
  const { setPaymentCard, setCardErrors } = useSubscriptionStore()
  const activePaymentMethods = useSubscriptionStore((state) => state.data.activePaymentMethods)
  const cardBrandRecognition = useSubscriptionStore((state) => state.card.cardBrandRecognition)
  const cardErrors = useSubscriptionStore((state) => state.card.cardErrors)

  if (id === 0 && activePaymentMethods?.includes('CARD')) {
    return (
      <>
        <>
          <TextField
            id='holderName'
            placeholder='Full Name *'
            variant='outlined'
            type='text'
            className='payment-form-input'
            onKeyDown={(evt) => {
              // Allow only letters and a single space between words
              const isValidKey =
                /^[a-zA-Z]$/.test(evt.key) ||
                (evt.key === ' ' && evt.target.value.slice(-1) !== ' ') ||
                evt.key === 'Backspace' ||
                evt.key === 'Delete' ||
                evt.key === 'ArrowLeft' ||
                evt.key === 'ArrowRight' ||
                evt.key === 'Tab'
              if (!isValidKey) {
                evt.preventDefault()
              }
            }}
            onChange={(event) => {
              const value = event.target.value
                .replace(/[^a-zA-Z ]/g, '')
                .replace(/\s+/g, ' ')
                .trim()
              setPaymentCard('cardHolderName', value)
              setCardErrors('cardHolderName', '')
            }}
          />
          {cardErrors?.cardHolderName && <Typography className='card-error'>{cardErrors?.cardHolderName}</Typography>}

          <div id='cardNumber' className='payment-form-input-card' />
          {cardErrors?.cardNumber && <Typography className='card-error'>{cardErrors?.cardNumber}</Typography>}

          {cardBrandRecognition === 'VI' && <img src={VisaImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'DI' && <img src={DiscoverImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'MC' && <img src={MasterImg} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'MD' && <img src={MaestroIcon} className='payment-form-card-brand' />}
          {cardBrandRecognition === 'AM' && <img src={AmexImg} className='payment-form-card-brand' />}

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100% !important',
              paddingTop: '10px',
              height: '60px'
            }}
          >
            <Box
              style={{
                width: '70%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start'
              }}
            >
              <div id='expiryDate' className='payment-form-date-input' />
              {cardErrors?.expiryDate && <Typography className='card-error'>{cardErrors?.expiryDate}</Typography>}
            </Box>

            <Box
              style={{
                width: '30%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start'
              }}
            >
              <div id='cvv' className='payment-form-cvv-input' />
              {cardErrors?.cvv && <Typography className='card-error'>{cardErrors?.cvv}</Typography>}
            </Box>
          </Box>
        </>
      </>
    )
  } else {
    return (
      <>
        <div hidden id='cardNumber' className='payment-form-input-card' />
        <div hidden id='expiryDate' className='payment-form-date-input' />
        <div hidden id='cvv' className='payment-form-cvv-input' />
      </>
    )
  }
}

const Payment = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const classes = useStyles()
  const paymentClasses = usePaymentStyles()
  const portalStore = usePortalStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const stateList = useStateStore((state) => state.stateList)
  const stateName = stateList?.find((x) => x?.state_id === Number(userDetails?.state))?.stateCode
  const { stepperCalledFor } = useStepperStore((state) => state)

  const [inprogressModalOpen, setInprogressModalOpen] = useState(false)

  // Paysafe Setup - New flow
  const ENV = import.meta.env.VITE_NODE_ENV
  const publicPaymentKey = import.meta.env.VITE_PUBLIC_PAYMENT_KEY
  const accountID = import.meta.env.VITE_PAYSAFE_ACCOUNT_ID
  const API_KEY = btoa(publicPaymentKey)

  // UTILS
  const browserType = getOSAndBrowser()
  const sessionId = useSeon()
  const isAppleDevice = browserType.os === 'MacOS' || browserType.os === 'iOS'

  // SETTERS - PAYMENT STORE
  const {
    setPaymentData,
    setPaymentToken,
    setPaymentStatus,
    setPaymentCard,
    setCardErrors
  } = useSubscriptionStore()

  // SUBSCRIPTION Getters
  const {
    selectedPlan,
    yearly,
    refundAmount,
    userSubscription,
    isUpgrade,
    isExclusiveBonusClaimed,
    exclusiveBonus
  } = useSubscriptionStore((state) => state)

  // TOKENS
  const { transactionId, singleUseCustomerToken, paymentHandleToken } = useSubscriptionStore((state) => state.tokens)

  // DATA
  const {
    depositInitiated,
    demoInstance,
    finalAmount,
    activePaymentMethods,
    selectedPaymentMethod,
    preferredPayments,
    selectedPreferredPayment,
    selectedPreferredPaymentId
  } = useSubscriptionStore((state) => state.data)

  // STATUS
  const { value, isLoading, paymentRedirection, paymentDisabled, showAllPayments } = useSubscriptionStore(
    (state) => state.status
  )

  // CARD
  const cardHolderName = useSubscriptionStore((state) => state.card.cardHolderName)
  const savePaymentToggle = useSubscriptionStore((state) => state.card.savePaymentToggle)
  const selectedSavedCard = useSubscriptionStore((state) => state.card.selectedSavedCard)

  // DEPOSIT STORE
  const setDepositTransactionId = useDepositInitStore((state) => state.setDepositTransactionId)
  const setDepositDetails = useDepositInitStore((state) => state.setDepositDetails)
  const setPaymentMethod = usePaymentProcessStore((state) => state.setPaymentMethod)
  const setPaymentDepositTransactionId = usePaymentProcessStore((state) => state.setPaymentDepositTransactionId)
  const setPaymentErrorState = usePaymentProcessStore((state) => state.setPaymentErrorState)

  const handleSelectPreferredPayment = (transId, prefPayment) => {
    if (isLoading) return
    if (selectedPreferredPaymentId === prefPayment?.paymentMethodName) {
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', null)
      setPaymentData('selectedPaymentMethod', null)
      setPaymentToken('paymentHandleToken', '')
      setPaymentCard('selectedSavedCard', '')
      setPaymentCard('cardHolderName', '')
    } else {
      setPaymentData('selectedPreferredPaymentId', prefPayment?.paymentMethodName)
      setPaymentData('selectedPreferredPayment', prefPayment)
      setPaymentData('selectedPaymentMethod', 4)
      setPaymentMethod(prefPayment?.paymentMethodType)
      if (prefPayment?.paymentMethodType === 'CARD') {
        setPaymentStatus('isLoading', true)
        setPaymentCard('selectedSavedCard', prefPayment?.paymentMethodName)
        setPaymentCard('cardHolderName', prefPayment?.moreDetails?.holderName)
        savedCardInitDeposit.mutate({
          transactionId: transId || transactionId,
          lastFourDigit: prefPayment?.moreDetails?.lastDigits
        })
      }
    }
  }

  // Change payment method
  const changePaymentMethod = (key) => {
    if (isLoading) return null
    setPaymentData('selectedPaymentMethod', key)
    setPaymentToken('paymentHandleToken', '')
    setPaymentCard('selectedSavedCard', '')
    setPaymentCard('cardHolderName', '')
    setPaymentStatus('paymentDisabled', false)
    setPaymentData('selectedPreferredPaymentId', '')
    setPaymentData('selectedPreferredPayment', null)
  }

  // useEffect(() => {
  //   TagManager.dataLayer({
  //     dataLayer: {
  //       event: 'checkout_payment_method',
  //       user_id: userDetails?.userId,
  //       email: userDetails?.email,
  //       phone: userDetails?.phoneNumber,
  //       item_id: packageDetails?.packageId,
  //       item_name: packageDetails?.packageName,
  //       price: packageDetails?.amount,
  //       catalog: packageDetails?.isSpecialPackage ? 'Special_Package' : 'Basic_Package',
  //       gcCoin: packageDetails?.gcCoin,
  //       scCoin: packageDetails?.scCoin
  //     }
  //   })
  // }, [])

  const handleGeocomplyPopup = (data) => {
    portalStore.openPortal(() => <GeocomplyPopup open errorData={data} />, 'tournamentEndPopup')
  }

  // Paysafe Setup - New flow
  // Deposit Success

  const successToggler = (result) => {
    if (result?.success === false) {
      setPaymentData('depositInitiated', true)
      setDepositDetails(result)
      setInprogressModalOpen(result?.success === false)
      setPaymentData('activePaymentMethods', result?.activePaymentMethods)
    } else {
      setDepositDetails(result)
      setInprogressModalOpen(!result?.success)
      setPaymentToken('transactionId', result?.transactionId)
      setDepositTransactionId({ depositTransactionId: result?.transactionId })
      window.localStorage.setItem('transactionId', result?.transactionId)
      window.localStorage.setItem('transactionSuccess', true)
      window.localStorage.setItem('isSubscription', true)
      setPaymentData('activePaymentMethods', result?.activePaymentMethods)
      setPaymentData('finalAmount', result?.amount)

      if (result?.preferredPayment?.length > 0) {
        setPaymentData('preferredPayments', result?.preferredPayment)
        handleSelectPreferredPayment(result?.transactionId, result?.preferredPayment[0])
      } else {
        setPaymentData('depositInitiated', true)
      }
    }
  }

  // Deposit Error
  const errorToggler = (error) => {
    if (error?.response?.data?.errors[0]?.name === 'KycRequiredError') {
      setPaymentData('depositInitiated', true)
      portalStore.openPortal(() => <VerifyModal packageDetails={selectedPlan} />, 'innerModal')
    } else if (error?.response?.data?.errors[0]?.name === 'DepositInitiatedError') {
      setPaymentData('depositInitiated', true)
      console.log('Payment Initialization Failed, Try again !', error?.response?.data?.errors[0])
      portalStore.closePortal()
    } else if (
      error?.response?.data?.data?.state === 'DECLINE' ||
      error?.response?.data?.data?.ipDetails?.vpn === true ||
      error?.response?.data?.data?.ipDetails?.web_proxy === true
    ) {
      localStorage.setItem('allowedUserAccess', false)
      handleGeocomplyPopup(error?.response?.data?.data)
    } else {
      setPaymentData('depositInitiated', true)
      console.log('Payment Initialization Failed, Try again !', error?.response?.data?.errors[0])
      portalStore.closePortal()
    }
    setPaymentErrorState({
      paymentError: error,
      paymentErrorMessage: 'Payment Initialization Failed'
    })
  }

  // Initalize Subscription Deposit
  const initDeposit = subscriptionQuery.useSubscriptionMutation({ successToggler, errorToggler })

  // useEffect(() => {
  //   if (sessionId) {
  //     initDeposit.mutate({
  //       subscriptionId: selectedPlan?.subscriptionId,
  //     })
  //   }
  // }, [sessionId])

  useEffect(() => {
    console.log('###IS_UPGRADE', !!userSubscription?.subscriptionDetail?.subscriptionId, isUpgrade)
    initDeposit.mutate({
      subscriptionId: selectedPlan?.subscriptionId,
      planType: yearly ? 'yearly' : 'monthly',
      upgradeSubscription: !!userSubscription?.subscriptionDetail?.subscriptionId && isUpgrade
    })
  }, [])

  // Cancel Deposit
  const setCancelDeposit = usePaymentProcessStore((state) => state.setCancelDeposit)
  const cancelDeposit = PaymentQuery.cancelDepositMutation({
    onSuccess: (res) => {
      setPaymentToken('transactionId', '')
      setPaymentData('depositInitiated', false)
      setCancelDeposit(false)
      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')

      if (res?.data?.success) {
        initDeposit.mutate({
          subscriptionId: selectedPlan?.subscriptionId,
          planType: yearly ? 'yearly' : 'monthly',
          upgradeSubscription: !!userSubscription?.subscriptionDetail?.subscriptionId
        })
      }
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
      setCancelDeposit(false)
    }
  })

  const paysafeCheckError = PaymentQuery.addPaymentErrorsMutation({
    onSuccess: (res) => {
      console.log('Successfully sent error to backend', res)
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
    }
  })

  // PAYSAFE INITIALIZATION

  const handlePaysafeMethodInit = async () => {
    try {
      switch (selectedPaymentMethod) {
        case 0:
          setPaymentStatus('isLoading', true)
          setCardErrors({})
          await setupPaysafeFields({
            API_KEY,
            options: {
              currencyCode: 'USD',
              environment: ENV === 'production' ? 'LIVE' : 'TEST',
              accounts: {
                default: +accountID
              },
              fields: {
                cardNumber: {
                  selector: '#cardNumber',
                  separator: ' ',
                  accessibilityLabel: 'Card number',
                  placeholder: 'Card Number *',
                  iframeTitle: 'Credit card details container',
                  accessibilityErrorMessage: 'Invalid card number',
                  autoComplete: 'off'
                },
                expiryDate: { selector: '#expiryDate', placeholder: 'MM/YY *', autoComplete: 'off' },
                cvv: { selector: '#cvv', placeholder: 'CVV *', mask: true, autoComplete: 'off' }
              },
              style: {
                input: {
                  'font-family': '"Rajdhani", sans-serif',
                  'font-size': '14px',
                  color: 'white'
                }
              }
            },
            onCardBrandRecognized: (brand) => {
              const brandMap = {
                Visa: 'VI',
                Discover: 'DI',
                MasterCard: 'MC',
                Maestro: 'MD',
                'American Express': 'AM',
                'Diners Club': 'DC'
              }
              setPaymentCard('cardBrandRecognition', brandMap[brand] || '')
            },
            onCardError: (field, message) => {
              setCardErrors(field, message)
            },
            onSuccess: (instance) => {
              setPaymentData('demoInstance', instance)
              setPaymentData('depositInitiated', true)
              setPaymentStatus('paymentDisabled', false)
              setPaymentStatus('isLoading', false)
            },
            onFailure: (error) => {
              console.error('Paysafe setup failed:', error)
              setPaymentStatus('isLoading', false)
              setPaymentStatus('paymentDisabled', false)
              portalStore.closePortal()
              navigate(`${location.pathname}?status=failed`)
            }
          })
          break
        case 5:
          setPaymentStatus('isLoading', false)
          setPaymentStatus('paymentDisabled', false)
          break

        default:
          setPaymentStatus('isLoading', false)
          setPaymentStatus('paymentDisabled', false)
          break
      }
    } catch (error) {
      setDepositTransactionId({ depositTransactionId: '' })
      console.error('Paysafe initialization error:', error)
      setPaymentStatus('paymentDisabled', true)
      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  }

  // On click PAY button
  const handlePayment = async () => {
    try {
      setPaymentStatus('isLoading', true)
      switch (selectedPaymentMethod) {
        case 0:
          await handleCardPayment({
            demoInstance,
            cardHolderName,
            selectedSavedCard,
            paymentHandleToken,
            singleUseCustomerToken,
            transactionId,
            finalAmount,
            userDetails,
            stateName,
            setCardErrors,
            setPaymentStatus,
            handlePaymentCompletion,
            paysafeCheckError,
            setDepositTransactionId,
            setPaymentErrorState,
            portalStore,
            navigate,
            location
          })
          break
        case 4:
          await handlePreferredPayment()
          break
        default:
          console.log('No Payment method selected')
          break
      }
    } catch (error) {
      console.error('No Payment Method Selected:', error)
      setDepositTransactionId({ depositTransactionId: '' })
    }
  }

  // SAVED CARD INIT DEPOSIT
  const savedCardInitDeposit = PaymentQuery.savedCardInitDepositMutation({
    onSuccess: (res) => {
      setPaymentStatus('isLoading', true)
      setPaymentToken('paymentHandleToken', res?.data?.data?.paymentHandleToken)
      setPaymentToken('singleUseCustomerToken', res?.data?.data?.singleUseCustomerToken)
      setupPaysafeFields({
        API_KEY,
        options: {
          currencyCode: 'USD',
          environment: ENV === 'production' ? 'LIVE' : 'TEST',
          accounts: {
            default: +accountID
          },
          fields: {
            cardNumber: {
              selector: '#cardNumber',
              placeholder: 'Card Number',
              optional: true
            },
            expiryDate: { selector: '#expiryDate', placeholder: 'MM/YY', optional: true },
            cvv: { selector: '#cvv', placeholder: 'CVV', optional: true, mask: true }
          },
          style: {
            input: {
              'font-family': '"Rajdhani", sans-serif',
              'font-size': '14px',
              color: 'white !important',
              '-webkit-text-fill-color': 'white !important'
            }
          }
        },
        onCardBrandRecognized: (brand) => {
          const brandMap = {
            Visa: 'VI',
            Discover: 'DI',
            MasterCard: 'MC',
            Maestro: 'MD',
            'American Express': 'AM',
            'Diners Club': 'DC'
          }
          setPaymentCard('cardBrandRecognition', brandMap[brand] || '')
        },
        onCardError: (field, message) => {
          setCardErrors(field, message)
        },
        onSuccess: (instance) => {
          setPaymentData('demoInstance', instance)
          setPaymentData('depositInitiated', true)
          setPaymentStatus('isLoading', false)
        },
        onFailure: (error) => {
          console.error('Paysafe setup failed:', error)
          setPaymentStatus('isLoading', false)
          portalStore.closePortal()
          navigate(`${location.pathname}?status=failed`)
        }
      })
    },
    onError: (error) => {
      console.log('Saved Card Init Deposit Error', error)
      setDepositTransactionId({ depositTransactionId: '' })
      setPaymentErrorState({
        paymentError: error,
        paymentErrorMessage: 'Payment Initialization Failed'
      })
      setPaymentStatus('isLoading', false)
      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  })

  // PREFERRED PAYMENT
  const handlePreferredPayment = async () => {
    if (selectedPreferredPayment?.paymentMethodType === 'CARD') {
      handleCardPayment({
        demoInstance,
        cardHolderName,
        selectedSavedCard,
        paymentHandleToken,
        singleUseCustomerToken,
        transactionId,
        finalAmount,
        userDetails,
        stateName,
        setCardErrors,
        setPaymentStatus,
        handlePaymentCompletion,
        paysafeCheckError,
        setDepositTransactionId,
        setPaymentErrorState,
        portalStore,
        navigate,
        location
      })
    }
  }

  // PROCESS PAYMENT
  const setPaymentState = usePaymentProcessStore((state) => state.setPaymentState)
  const paymentCompletion = subscriptionQuery.useProcessSubscriptionMutation({
    onSuccess: (res) => {
      if (res?.data?.success) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'purchase',
            amount: res?.data?.data?.amount,
            currency: 'USD',
            scCoin: res?.data?.data?.scCoin,
            gcCoin: res?.data?.data?.gcCoin,
            isFirstDeposit: res?.data?.data?.isFirstDeposit,
            totalPurchaseSum: res?.data?.data?.totalPurchaseSum,
            payment_type: 'Deposit',
            payment_method: res?.data?.data?.paymentMethod,
            transaction_id: res?.data?.data?.transactionId,
            item_id: res?.data?.data?.packageId,
            item_name: res?.data?.data?.packageName,
            user_id: userDetails?.userId,
            user_detail: {
              data: [
                {
                  first_name: userDetails?.firstName,
                  last_name: userDetails?.lastName,
                  email: userDetails?.email,
                  dob: userDetails?.dateOfBirth,
                  gender: userDetails?.gender,
                  phone: userDetails?.phone,
                  city: userDetails?.city,
                  state: stateName,
                  zipcode: userDetails?.zipCode,
                  country: 'US'
                }
              ]
            }
          }
        })

        setPaymentState(res?.data?.data)
        navigate(
          `${location.pathname}?status=success&transactionId=${transactionId}&paymentMethod=${selectedPaymentMethod}`
        )
      } else {
        setDepositTransactionId({ depositTransactionId: '' })
        cancelDeposit.mutate({ transactionId: transactionId })
        navigate(`${location.pathname}?status=failed`)
        setPaymentErrorState({
          paymentError: res,
          paymentErrorMessage: res?.data?.message
        })
      }
      setPaymentToken('transactionId', '')
      setPaymentToken('paymentHandleToken', '')
      setPaymentToken('singleUseCustomerToken', '')
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', '')
      setPaymentStatus('isLoading', false)

      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: ''
      })

      portalStore.closePortal()
    },
    onError: (error) => {
      console.log('Paysafe Payment Fallback Error', error)
      setPaymentErrorState({
        paymentError: error,
        paymentErrorMessage: error?.data?.message
      })
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: ''
      })
      setDepositTransactionId({ depositTransactionId: '' })
      cancelDeposit.mutate({ transactionId: transactionId })
      setPaymentToken('transactionId', '')
      setPaymentToken('paymentHandleToken', '')
      setPaymentToken('singleUseCustomerToken', '')
      setPaymentData('selectedPreferredPayment', null)
      setPaymentData('selectedPreferredPaymentId', '')
      setPaymentStatus('isLoading', false)

      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')

      portalStore.closePortal()
      navigate(`${location.pathname}?status=failed`)
    }
  })

  // Fallbacks
  const handlePaymentCompletion = (props) => {
    const payload = {
      transactionId: transactionId,
      paymentMethod: props?.paymentMethod
    }
    if (props?.paymentMethod === 'CARD') {
      payload.paymentHandleToken = props?.token
      if (savePaymentToggle) {
        payload.saveCard = true
      }
    } else {
      payload.depositTransactionId = props?.depositTransactionId
    }
    paymentCompletion.mutate(payload)
  }

  useEffect(() => {
    if (depositInitiated) {
      if (selectedPaymentMethod === null) {
        setPaymentStatus('paymentDisabled', true)
      } else {
        handlePaysafeMethodInit()
      }
    }
  }, [depositInitiated, selectedPaymentMethod])

  return (
    <>
      <Grid>
        <Box className='verification-code-container'>
          <Typography className='verification-code-text'>PAYMENT METHOD</Typography>
          <PaymentTimer initialTime={10 * 60} onTimeout={() => portalStore.closePortal()} />
        </Box>
        <Box sx={{ padding: '0 10px' }} className='small-subheader-container'>
          <Typography className='small-subheader-text'>Secure & Convenient Payment methods.</Typography>
        </Box>
        <Grid className={paymentClasses.paymentStyleModal}>
          <Grid className='modal-section-container'>
            <Grid className='paymentoptionImage-Rightcontainer'>
              <Box className={classes.profileTabsDesign}>
                <Box className='profile-section-detail'>
                  {value === 'payment' && (
                    <Grid className='payment-modal-content'>
                      <Grid sx={!depositInitiated ? { width: '100%', display: 'hidden' } : { width: '100%' }}>
                        <PurchaseSummaryAccordion
                          yearly={yearly}
                          packageDetails={selectedPlan}
                          finalAmount={finalAmount}
                          refundAmount={refundAmount}
                          isExclusiveBonusClaimed={isExclusiveBonusClaimed}
                          exclusiveBonus={exclusiveBonus}
                        />
                      </Grid>
                      {(!depositInitiated || paymentRedirection) && <PaymentLoader />}
                      <div style={!depositInitiated ? { display: 'hidden' } : {}}>
                        {preferredPayments?.length > 0 && (
                          <Grid className='preferred-payment-container'>
                            <Typography className='payment-methods-title'>Preferred Payment</Typography>
                            <Box className='preferred-box'>
                              {(showAllPayments ? preferredPayments : preferredPayments.slice(0, 3)).map(
                                (preferredPayment, index) => (
                                  <PreferredPaymentCard
                                    preferredPayment={preferredPayment}
                                    key={index}
                                    selectedPreferredPaymentId={selectedPreferredPaymentId}
                                    handleSelectPreferredPayment={handleSelectPreferredPayment}
                                  />
                                )
                              )}
                            </Box>
                            {preferredPayments.length > 3 && (
                              <Typography
                                className='payment-methods-other-title'
                                onClick={() => setPaymentStatus('showAllPayments', !showAllPayments)}
                              >
                                {showAllPayments ? 'Hide' : 'Show'} Other Saved Payment Methods
                              </Typography>
                            )}
                          </Grid>
                        )}

                        <Grid>
                          <Typography className='payment-methods-title'>Payment Methods</Typography>
                          <Grid container className='payment-methods'>
                            {activePaymentMethods?.includes('CARD') && (
                              <Grid item xs={12} sm={12} md={12}>
                                <Box
                                  className={`payment-methods-item ${selectedPaymentMethod === 0 ? 'active' : ''}`}
                                  onClick={() => {
                                    changePaymentMethod(0)
                                    setPaymentMethod('CARD')
                                  }}
                                >
                                  <img src={CardImg} alt='card-img' height='30px' width='30px' />
                                  <Typography sx={{ color: 'white', fontWeight: '500' }}>Card</Typography>
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                        </Grid>
                        <Box className={[0].includes(selectedPaymentMethod) ? 'payment-form-container' : ''}>
                          <PaymentMethodForm id={selectedPaymentMethod} />
                          {selectedPaymentMethod === 0 && (
                            <Box className='payment-form-save-card-box'>
                              <Typography sx={{ color: 'white', width: '60%' }}>
                                Saving this card for auto debit.
                              </Typography>

                              <FormControlLabel
                                control={
                                  <CustomSwitch
                                    checked={savePaymentToggle}
                                    color='warning'
                                  />
                                }
                                label=''
                              />
                            </Box>
                          )}
                        </Box>
                        <Grid className='order-summary-card' sx={{ color: '#FFFFFF' }}>
                          <Grid className='payment-method-section'>
                            <Grid className='payment-btn-wrap'>
                              <Grid className='btnWhiteGradient'>
                                <Box className='payment-btn-box'>
                                  {selectedPreferredPayment && (
                                    <PreferredPaymentBox
                                      selectedPreferredPayment={selectedPreferredPayment}
                                      isAppleDevice={isAppleDevice}
                                      paymentIcons={{
                                        VI: VisaImg,
                                        DI: DiscoverImg,
                                        MC: MasterImg,
                                        MD: MaestroIcon,
                                        AM: AmexImg,
                                        PAY_BY_BANK: Bank,
                                        SKRILL: skrill,
                                        APPLE_PAY: applePayIcon
                                      }}
                                    />
                                  )}
                                  <Box
                                    style={{
                                      display: 'flex',
                                      justifyContent: !selectedPreferredPayment ? 'center' : 'end'
                                    }}
                                  >
                                    <Button
                                      variant='contained'
                                      className={
                                        selectedPaymentMethod === null || paymentDisabled || isLoading
                                          ? 'disabled'
                                          : 'btn-gradient'
                                      }
                                      data-tracking='Store.Checkout.Step3.Payment.Btn'
                                      data-tracking-caller={stepperCalledFor}
                                      onClick={handlePayment}
                                      disabled={selectedPaymentMethod === null || paymentDisabled || isLoading}
                                    >
                                      {isLoading ? <BtnLoader /> : `Pay ${finalAmount?.toFixed(2)}`}
                                    </Button>
                                  </Box>
                                </Box>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                      </div>
                    </Grid>
                  )}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Grid>
        <DepositInprogressModal
          inprogressModalOpen={inprogressModalOpen}
          setInprogressModalOpen={setInprogressModalOpen}
          packageDetails={selectedPlan}
          isAppleDevice={isAppleDevice}
          successToggler={successToggler}
          errorToggler={errorToggler}
          sessionId={sessionId}
          isSubscription
        />
      </Grid>
    </>
  )
}

export default Payment
