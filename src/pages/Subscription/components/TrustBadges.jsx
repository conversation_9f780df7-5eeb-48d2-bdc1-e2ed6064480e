import React from 'react'
import { Box, Typography } from '@mui/material'
import { makeStyles } from '@mui/styles'
import ShieldIcon from '@mui/icons-material/Security'
import CheckIcon from '@mui/icons-material/CheckCircle'
import StarIcon from '@mui/icons-material/Star'
import UsersIcon from '@mui/icons-material/Group'

const useStyles = makeStyles((theme) => ({
  section: {
    backgroundColor: '#0f172a', // brand-dark
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1)
  },
  container: {
    maxWidth: 960,
    margin: '0 auto',
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing(4),
    opacity: 0.6
  },
  badge: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1)
  },
  iconAccent: {
    color: '#f59e0b' // brand-accent
  },
  iconGreen: {
    color: '#34d399' // green-400
  },
  label: {
    color: '#ffffff',
    fontSize: '1rem !important',
    fontWeight: '700 !important'
  }
}))

const TrustBadges = () => {
  const classes = useStyles()

  return (
    <Box className={classes.section}>
      <Box className={classes.container}>
        <Box className={classes.badge}>
          <ShieldIcon className={classes.iconAccent} />
          <Typography className={classes.label}>SSL Secured</Typography>
        </Box>
        <Box className={classes.badge}>
          <CheckIcon className={classes.iconGreen} />
          <Typography className={classes.label}>Verified Reviews</Typography>
        </Box>
        <Box className={classes.badge}>
          <StarIcon className={classes.iconAccent} />
          <Typography className={classes.label}>Award Winning</Typography>
        </Box>
        <Box className={classes.badge}>
          <UsersIcon className={classes.iconAccent} />
          <Typography className={classes.label}>50K+ Members</Typography>
        </Box>
      </Box>
    </Box>
  )
}

export default TrustBadges
