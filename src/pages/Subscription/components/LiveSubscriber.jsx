import { Box, Paper, Snackbar, Typography } from '@mui/material'
import { makeStyles } from '@mui/styles'
import React from 'react'
import ActivityIcon from '@mui/icons-material/TrendingUp'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'fixed',
    bottom: theme.spacing(2),
    left: '14vw',
    zIndex: 40,
    maxWidth: 320,
    [theme.breakpoints.down(1200)]: {
      bottom: theme.spacing(4),
      left: '3vw'
    }
  },
  toast: {
    backgroundColor: '#0f172a',
    border: `1px solid ${theme.colors?.YellowishOrange || '#f59e0b'}`,
    borderRadius: theme.spacing(1),
    padding: theme.spacing(1),
    boxShadow: theme.shadows[12],
    transition: 'transform 0.5s',
    '&:hover': {
      transform: 'scale(1.05)'
    }
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    marginBottom: theme.spacing(1)
  },
  pulseIcon: {
    width: 16,
    height: 16,
    color: theme.colors?.YellowishOrange || '#f59e0b',
    animation: '$pulse 1.5s infinite ease-in-out'
  },
  liveText: {
    color: theme.colors?.YellowishOrange || '#f59e0b',
    fontSize: '1rem !important',
    fontWeight: '700 !important',
    textTransform: 'uppercase',
    letterSpacing: 1
  },
  bodyText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600'
  },
  faded: {
    color: '#9ca3af',
    fontWeight: '600'
  },
  actionText: {
    color: theme.colors?.YellowishOrange || '#f59e0b',
    fontWeight: '800 !important'
  },
  timeText: {
    color: '#9ca3af',
    fontSize: 11,
    fontWeight: '800 !important',
    marginTop: theme.spacing(0.5)
  },
  '@keyframes pulse': {
    '0%': { transform: 'scale(1)', opacity: 1 },
    '50%': { transform: 'scale(1.2)', opacity: 0.6 },
    '100%': { transform: 'scale(1)', opacity: 1 }
  }
}))

// Helper function to parse message
const parseMessage = (msg) => {
  const pattern = /^(.+?) from (.+?) (upgraded to|joined|claimed|subscribed to) (.+?)[!@<>\s]*$/i
  const match = msg.match(pattern)

  if (match) {
    return {
      name: match[1].trim(),
      location: match[2].trim(),
      action: match[3].trim(),
      plan: match[4].trim()
    }
  }
}

const LiveSubscriber = ({ snackOpen, handleSnackClose, dataMsg }) => {
  const classes = useStyles()
  const activity = parseMessage(dataMsg)

  return (
    <Snackbar open={snackOpen} autoHideDuration={4000} onClose={handleSnackClose}>
      <Box className={classes.root}>
        <Paper className={classes.toast}>
          <Box className={classes.header}>
            <ActivityIcon className={classes.pulseIcon} />
            <Typography className={classes.liveText}>Live Activity</Typography>
          </Box>
          <Box>
            <Typography className={classes.bodyText}>
              <strong>{activity.name}</strong>
              <span className={classes.faded}> from </span>
              <strong>{activity.location}</strong>
            </Typography>
            <Typography className={classes.actionText}>
              {activity.action} {activity.plan}!
            </Typography>
          </Box>
          <Typography className={classes.timeText}>Just now</Typography>
        </Paper>
      </Box>
    </Snackbar>
  )
}

export default LiveSubscriber
