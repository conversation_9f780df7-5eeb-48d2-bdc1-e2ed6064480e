import React from 'react'
import { makeStyles } from '@mui/styles'
import { Box, Stack } from '@mui/material'
import useIntercom from '../../../components/SideBar/hooks/useIntercom'

const useStyles = makeStyles((theme) => ({
  '@keyframes pulse': {
    '0%': { opacity: 1 },
    '50%': { opacity: 0.5 },
    '100%': { opacity: 1 }
  },
  footerCtaBox: {
    backgroundColor: theme.colors.YellowishOrange,
    paddingTop: theme.spacing(4),
    paddingBottom: theme.spacing(4),
    textAlign: 'center',
    [theme.breakpoints.down('md')]: {
      padding: '2rem 1rem'
    }
  },
  footerCtaHeading: {
    color: theme.colors.textBlack,
    marginBottom: theme.spacing(2),
    fontWeight: 700,
    fontSize: '2rem',
    [theme.breakpoints.up('sm')]: {
      fontSize: '2.5rem'
    },
    [theme.breakpoints.up('md')]: {
      fontSize: '3rem'
    }
  },
  footerCtaSubheading: {
    fontWeight: 700,
    color: theme.colors.textBlack,
    fontSize: '1.25rem',
    marginBottom: theme.spacing(2),
    [theme.breakpoints.up('sm')]: {
      fontSize: '1.125rem'
    },
    [theme.breakpoints.up('md')]: {
      fontSize: '1.25rem'
    }
  },
  footerCtaHighlights: {
    fontWeight: 700,
    color: theme.colors.textBlack,
    fontSize: '0.875rem',
    marginBottom: theme.spacing(2),
    [theme.breakpoints.up('sm')]: {
      fontSize: '1rem'
    },
    [theme.breakpoints.up('md')]: {
      fontSize: '1.125rem'
    }
  },
  footerCtaButtonPrimary: {
    backgroundColor: theme.colors.textBlack,
    color: theme.colors.YellowishOrange,
    fontWeight: 'bold',
    padding: theme.spacing(1.5, 3),
    borderRadius: 16,
    textTransform: 'uppercase',
    fontSize: '1rem',
    animation: '$pulse 2s infinite',
    '&:hover': {
      boxShadow: theme.shadows[6],
      transform: 'translateY(-2px)',
      transition: 'all 0.2s ease-in-out'
    }
  },
  footerCtaButtonSecondary: {
    backgroundColor: theme.colors.YellowishOrange,
    border: '2px solid #1F2937',
    color: theme.colors.textBlack,
    fontWeight: 'bold',
    padding: theme.spacing(1.5, 3),
    borderRadius: 16,
    textTransform: 'uppercase',
    fontSize: '1rem',
    '&:hover': {
      backgroundColor: theme.colors.textBlack,
      color: theme.colors.YellowishOrange
    }
  },
  footerCtaNote: {
    marginTop: theme.spacing(2),
    fontSize: '0.75rem',
    color: theme.colors.textBlack,
    fontWeight: 500,
    [theme.breakpoints.up('md')]: {
      fontSize: '0.875rem'
    }
  }
}))

const FooterCTA = () => {
  const classes = useStyles()

  return (
    <Box className={classes.footerCtaBox} aria-labelledby='footer-cta-heading'>
      <h2 className={classes.footerCtaHeading}>Ready to Level Up?</h2>

      <h6 className={classes.footerCtaSubheading}>Join thousands of players already enjoying premium benefits</h6>

      <h6 className={classes.footerCtaHighlights}>
        🎯 Start winning more • 🚀 Get exclusive access • 💰 Save up to $150/year with yearly plans
      </h6>

      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent='center' alignItems='center'>
        <button
          className={classes.footerCtaButtonPrimary}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          🚀 CLAIM YOUR SPOT NOW
        </button>

        <button onClick={() => useIntercom(true)} className={classes.footerCtaButtonSecondary}>💬 TALK TO SUPPORT</button>
      </Stack>

      <h6 className={classes.footerCtaNote}>✅ Cancel Anytime • 🔒 Secure Payment • 📱 Instant Access</h6>
    </Box>
  )
}

export default FooterCTA
