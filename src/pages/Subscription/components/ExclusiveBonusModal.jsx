import React from 'react'
import { makeStyles } from '@mui/styles'
import { Box, Typography, Button, Paper, Dialog, IconButton } from '@mui/material'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'
import CloseIcon from '@mui/icons-material/Close'
import subscriptionQuery from '../../../reactQuery/subscriptionQuery'

const useStyles = makeStyles((theme) => ({
  overlay: {
    position: 'fixed',
    inset: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1300,
    padding: theme.spacing(2)
  },
  popup: {
    backgroundColor: '#111827', // brand-darker
    borderRadius: 16,
    padding: theme.spacing(2),
    maxWidth: 400,
    width: '100%',
    border: '2px solid #FDB72E', // brand-accent (yellow)
    boxShadow: theme.shadows[8],
    animation: '$pulse 1.5s infinite'
  },
  header: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  title: {
    color: '#fff',
    fontSize: '1.5rem !important',
    fontWeight: '700 !important',
    textAlign: 'center'
  },
  closeBtn: {
    color: '#fff !important',
    top: -70,
    left: '95%'
  },
  bonusBadge: {
    backgroundColor: '#FDB72E',
    color: '#111827',
    padding: theme.spacing(1, 2),
    borderRadius: 999,
    fontWeight: 'bold',
    fontSize: '1.125rem',
    marginBottom: theme.spacing(1),
    animation: '$bounce 1s infinite',
    display: 'inline-block'
  },
  bonusText: {
    color: '#fff',
    fontWeight: '700 !important',
    marginBottom: theme.spacing(1)
  },
  bonusHighlight: {
    color: '#FDB72E',
    fontWeight: 'bold'
  },
  description: {
    color: '#9ca3af',
    fontSize: '1rem !important',
    fontWeight: '700 !important',
    marginBottom: `${theme.spacing(1)} !important`
  },
  dismissBtn: {
    marginTop: `${theme.spacing(1.5)} !important`,
    color: '#9ca3af !important',
    fontSize: '1rem !important',
    '&:hover': {
      marginTop: `${theme.spacing(1.5)} !important`,
      color: '#fff !important',
      fontSize: '1rem !important'
    },
    width: '100%'
  },
  '@keyframes bounce': {
    '0%, 100%': { transform: 'translateY(0)' },
    '50%': { transform: 'translateY(-6px)' }
  },
  '@keyframes pulse': {
    '0%, 100%': { transform: 'scale(1)' },
    '50%': { transform: 'scale(1.02)' }
  }
}))

export default function ExclusiveBonusModal ({ exclusiveBonusOpen }) {
  const classes = useStyles()
  const {
    setExclusiveBonusOpen,
    exclusiveBonusModalData,
    setExclusiveBonus,
    setIsExclusiveBonusClaimed
  } = useSubscriptionStore((state) => state)
  const transactionId = window.localStorage.getItem('transactionId')

  const claimRetentionBonusMutation = subscriptionQuery.claimRetentionBonusMutation({
    onSuccess: (res) => {
      setExclusiveBonus(res?.data?.data)
      setIsExclusiveBonusClaimed(true)
      setExclusiveBonusOpen(false)
    },
    onError: (error) => {
      console.log('####CLAIM_RETENTION_BONUS_ERROR', error)
    }
  })

  const handleOnConfirm = () => {
    claimRetentionBonusMutation.mutate({
      transactionId: transactionId
    })
  }

  const handleOnClose = () => {
    setIsExclusiveBonusClaimed(true)
    setExclusiveBonusOpen(false)
    setExclusiveBonus(null)
  }

  return (
    <Dialog open={exclusiveBonusOpen}>
      <Box className={classes.overlay}>
        <Paper className={classes.popup}>
          {/* Header */}
          <Box className={classes.header}>
            <Typography variant='h6' className={classes.title}>
              Wait! Don't Leave Yet! 🎯
            </Typography>
          </Box>
          <IconButton onClick={handleOnClose} className={classes.closeBtn} size='large'>
            <CloseIcon size={24} />
          </IconButton>

          {/* Content */}
          <Box textAlign='center'>
            <div className={classes.bonusBadge}>🎁 EXCLUSIVE BONUS!</div>

            <Typography className={classes.bonusText}>
              Get an extra{' '}
              <span className={classes.bonusHighlight}>{exclusiveBonusModalData?.data?.gcAmount} GC + {exclusiveBonusModalData?.data?.scAmount} SC</span> if you subscribe in the next 5 minutes!
            </Typography>

            <Typography className={classes.description}>
              {exclusiveBonusModalData?.message}
            </Typography>

            <Button className='btn btn-primary' onClick={handleOnConfirm} fullWidth>
              🚀 CLAIM MY BONUS NOW
            </Button>
            <Button className={classes.dismissBtn} onClick={handleOnClose} fullWidth>
              No thanks, I'll pass on the bonus
            </Button>
          </Box>
        </Paper>
      </Box>
    </Dialog>
  )
}
