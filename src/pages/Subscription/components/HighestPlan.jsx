import React from 'react'
import { Box, Typography } from '@mui/material'
import { makeStyles } from '@mui/styles'
import { TMFPlus } from '../../../components/ui-kit/icons/svg'
import { HighestPlanImg } from '../../../components/ui-kit/icons/webp'
import LazyImage from '../../../utils/lazyImage'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'
import subscriptionBanner from '../../../components/ui-kit/icons/webp/subscription-banner.webp'

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#000',
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing(4),
    backgroundImage: `url(${subscriptionBanner})`,
    backgroundSize: 'cover',
    backgroundPosition: 'right',
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1.5)
    }
  },
  container: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    [theme.breakpoints.down('md')]: {
      flexDirection: 'column'
    }
  },
  leftSection: {
    flex: 1,
    paddingRight: theme.spacing(4),
    [theme.breakpoints.down('md')]: {
      paddingRight: 0,
      marginBottom: theme.spacing(4)
    }
  },
  premiumText: {
    fontSize: '2.25rem !important',
    fontWeight: 'bold !important',
    marginLeft: theme.spacing(1),
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    background: 'linear-gradient(180deg, #FFFFFF 27.34%, #7E7D7D 73.44%)'
  },
  rightSection: {
    width: '50%',
    flex: 1,
    display: 'flex',
    justifyContent: 'center',
    [theme.breakpoints.down('md')]: {
      width: '100%'
    },
    '& img': {
      maxWidth: '500px',
      width: '100%'
    }
  },
  iconWrapper: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(1)
  },
  plusIcon: {
    width: 32,
    height: 32,
    margin: theme.spacing(0, 1)
  },
  texth5: {
    fontSize: '2rem !important',
    fontWeight: 'bold !important',
    color: '#f7c518',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    background: 'linear-gradient(180deg, #F7E041 21.88%, #FA8C00 73.44%)'
  }
}))

const HighestPlan = () => {
  const classes = useStyles()
  const { userSubscription } = useSubscriptionStore((state) => state)

  const formatted = (endTime) => {
    return new Date(endTime).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit'
    })
  }

  return (
    <Box className={classes.root}>
      <Box className={classes.container}>
        <Box className={classes.leftSection}>
          <Box className={classes.iconWrapper}>
            <LazyImage src={TMFPlus} alt='TMF+' sizes='200px' priority />
            <Typography variant='h4' component='span' className={classes.premiumText}>
              {userSubscription?.subscriptionDetail?.name}
            </Typography>
          </Box>

          <Typography variant='h5' className={classes.texth5}>
            CONGRATULATIONS
          </Typography>
          <Typography variant='body1' sx={{ fontSize: '1.25rem', fontWeight: 'bold' }}>
            You’ve subscribed to TMF PLUS {userSubscription?.subscriptionDetail?.name}.
          </Typography>

          <Typography variant='h5' className={classes.texth5}>
            MEMBERSHIP IS ACTIVE
          </Typography>
          <Typography variant='body2' sx={{ fontSize: '1.25rem', fontWeight: 'bold', marginTop: 1 }}>
            Renewal on {formatted(userSubscription?.userSubscriptionDetail?.endDate)}
          </Typography>
        </Box>

        <Box className={classes.rightSection}>
          <LazyImage src={HighestPlanImg} alt='HighestPlanImg' priority />
        </Box>
      </Box>
    </Box>
  )
}

export default HighestPlan
