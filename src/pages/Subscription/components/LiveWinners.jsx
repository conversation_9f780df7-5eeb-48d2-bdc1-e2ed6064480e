import { useEffect, useState } from 'react'
import { <PERSON>, Typo<PERSON>, Card, Stack, Grid, CircularProgress, Alert } from '@mui/material'
import { makeStyles } from '@mui/styles'
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'
import { CasinoQuery } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { liveWinnerSocket } from '../../../utils/socket'

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(4, 2),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 0.5)
    }
  },
  heading: {
    textAlign: 'center',
    marginBottom: theme.spacing(2)
  },
  liveText: {
    color: 'white',
    fontSize: '2rem !important',
    fontWeight: '700 !important',
    [theme.breakpoints.down('md')]: {
      fontSize: '1.5rem !important'
    }
  },
  justNowText: {
    color: 'rgba(13, 239, 164, 0.91)',
    fontSize: '1.25rem !important',
    fontWeight: '700 !important',
    animation: '$pulse 1.5s infinite ease-in-out',
    [theme.breakpoints.down('md')]: {
      fontSize: '1rem !important'
    }
  },
  subText: {
    color: 'gray',
    fontSize: '1rem !important',
    fontWeight: '700 !important'
  },
  liveCard: {
    padding: theme.spacing(2),
    borderRadius: '30 !important',
    marginBottom: theme.spacing(2),
    background: 'linear-gradient(to right, rgba(16,185,129,0.2), rgba(5,150,105,0.2))',
    border: '2px solid rgba(16,185,129,0.5)',
    boxShadow: theme.shadows[10],
    transition: 'all 0.5s ease', // ✅ applies to transform & shadow
    [theme.breakpoints.down('md')]: {
      margin: '0 0 1rem 0 !important'
    },
    '&:hover': {
      transform: 'scale(1.05)', // ✅ increases width & height smoothly
      boxShadow: '0 0 25px rgba(14, 236, 162, 0.5)'
    }
  },
  cardHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2)
  },
  winnerAmount: {
    color: 'rgba(13, 239, 164, 0.91)',
    fontSize: '3rem !important',
    fontWeight: '700 !important',
    animation: '$pulse 1.5s infinite ease-in-out'
  },
  winnerName: {
    color: 'white',
    fontSize: '2rem !important',
    fontWeight: '700 !important'
  },
  recentBox: {
    backgroundColor: '#1e1e1e',
    border: '1px solid #374151',
    borderRadius: 12,
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    }
  },
  recentItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1),
    borderRadius: 10,
    backgroundColor: '#2d2d2d'
  },
  recentItemActive: {
    backgroundColor: 'rgba(34,197,94,0.1)',
    border: '1px solid rgba(34,197,94,0.3)'
  },
  statsCard: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1e1e1e',
    border: '1px solid #374151',
    padding: theme.spacing(1),
    [theme.breakpoints.down('md')]: {
      margin: '0 !important'
    }
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '200px',
    flexDirection: 'column',
    gap: theme.spacing(2)
  },
  errorContainer: {
    padding: theme.spacing(2),
    textAlign: 'center'
  },
  emptyState: {
    textAlign: 'center',
    padding: theme.spacing(4),
    color: 'gray'
  },
  pulseText: {
    animation: '$pulse 1.5s infinite ease-in-out'
  },
  '@keyframes pulse': {
    '0%, 100%': {
      opacity: 1
    },
    '50%': {
      opacity: 0.5
    }
  }
}))

const LiveWinners = () => {
  const classes = useStyles()
  const currentWinner = 0

  const liveWinnerSocketConnection = useUserStore((state) => state.liveWinnerSocketConnection)
  const [liveWinnersData, setLiveWinnersData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  const { data, isError, isLoading: queryLoading } = CasinoQuery.getLiveWinners({
    params: {},
    enabled: true
  })

  // Handle initial data loading
  useEffect(() => {
    if (data?.data?.data) {
      setLiveWinnersData(data.data.data)
      setIsLoading(false)
      setError(null)
    } else if (isError) {
      setError('Failed to load live winners data')
      setIsLoading(false)
    } else if (!queryLoading && (!data || !data?.data?.data)) {
      setIsLoading(false)
    }
  }, [data, isError, queryLoading])

  const onGetLiveWinners = (winnerData) => {
    if (winnerData?.data) {
      setLiveWinnersData((winnerList) => {
        const updatedWinners = [winnerData.data, ...winnerList.slice(0, Math.max(0, winnerList.length - 1))]
        return updatedWinners
      })
    }
  }

  useEffect(() => {
    if (liveWinnerSocketConnection) {
      liveWinnerSocket.on('LIVE_GAME_WINNERS', (data) => {
        onGetLiveWinners(data)
      })
    }
    return () => {
      liveWinnerSocket.off('LIVE_GAME_WINNERS', () => { })
    }
  }, [liveWinnerSocketConnection])

  // Loading state
  if (isLoading || queryLoading) {
    return (
      <Box className={classes.root}>
        <Box className={classes.loadingContainer}>
          <CircularProgress color='primary' />
          <Typography color='white' variant='body2'>
            Loading live winners...
          </Typography>
        </Box>
      </Box>
    )
  }

  // Error state
  if (error) {
    return (
      <Box className={classes.root}>
        <Box className={classes.errorContainer}>
          <Alert severity='error' sx={{ backgroundColor: '#1e1e1e', color: 'white' }}>
            {error}
          </Alert>
        </Box>
      </Box>
    )
  }

  // Empty state
  if (!liveWinnersData || liveWinnersData.length === 0) {
    return (
      <Box className={classes.root}>
        <Box className={classes.heading}>
          <Stack direction='row' justifyContent='center' alignItems='center' spacing={1} mb={1}>
            <FiberManualRecordIcon color='success' fontSize='small' />
            <Typography variant='h5' className={classes.liveText}>
              🏆 LIVE WINNERS
            </Typography>
            <FiberManualRecordIcon color='success' fontSize='small' />
          </Stack>
          <Typography variant='body2' className={classes.subText}>
            Real players winning real money right now!
          </Typography>
        </Box>
        <Box className={classes.emptyState}>
          <Typography color='white' variant='h3'>
            No live winners data available at the moment
          </Typography>
          <Typography color='white' variant='h4' mt={1}>
            Check back soon for the latest winners!
          </Typography>
        </Box>
      </Box>
    )
  }

  return (
    <Box className={classes.root}>
      <Box className={classes.heading}>
        <Stack direction='row' justifyContent='center' alignItems='center' spacing={1} mb={1}>
          <FiberManualRecordIcon color='success' fontSize='small' />
          <Typography variant='h5' className={classes.liveText}>
            🏆 LIVE WINNERS
          </Typography>
          <FiberManualRecordIcon color='success' fontSize='small' />
        </Stack>
        <Typography variant='body2' className={classes.subText}>
          Real players winning real money right now!
        </Typography>
      </Box>

      {/* Winner Card */}
      <Card className={classes.liveCard}>
        <Box className={classes.cardHeader}>
          <Stack direction='row' alignItems='center' spacing={1}>
            <FiberManualRecordIcon style={{ color: 'rgba(13, 239, 164, 0.91)' }} fontSize='small' />
            <Typography className={classes.justNowText} fontWeight='bold' variant='body2'>
              🎉 JUST WON
            </Typography>
          </Stack>
        </Box>

        <Box textAlign='center'>
          <Typography variant='h3' className={classes.winnerAmount} mb={1}>
            {liveWinnersData[currentWinner]?.winAmount?.toLocaleString() || '0'}
            {liveWinnersData[currentWinner]?.isScActive ? ' SC' : ' GC'}
          </Typography>
          <Typography className={classes.winnerName} variant='h6' color='white'>
            {liveWinnersData[currentWinner]?.username || 'Anonymous'}
          </Typography>
          <Typography variant='body2' color='white' mb={1} style={{ fontSize: '1rem', fontWeight: '700' }}>
            Playing <strong style={{ color: '#FDB72E' }}>{liveWinnersData[currentWinner]?.providerName || 'Unknown Game'}</strong>
          </Typography>
        </Box>
      </Card>

      {/* Recent Winners */}
      <Box className={classes.recentBox}>
        <Box display='flex' justifyContent='space-between' alignItems='center' mb={2}>
          <Typography variant='body2' color='white' style={{ fontSize: '1rem', fontWeight: '700' }}>
            Recent Winners (Last Hour)
          </Typography>
          <Stack direction='row' alignItems='center'>
            <FiberManualRecordIcon className={classes.pulseText} style={{ color: 'rgba(13, 239, 164, 0.91)' }} fontSize='small' />
            <Typography className={classes.pulseText} variant='caption' color='success.main' fontWeight='bold' paddingLeft={0.25}>
              LIVE
            </Typography>
          </Stack>
        </Box>

        <Stack spacing={0.5}>
          {liveWinnersData?.slice(1, 5).map((winner, i) => (
            <Box
              key={winner?.id || `winner-${i}-${winner?.username || 'anonymous'}`}
              className={`${classes.recentItem} ${i === currentWinner ? classes.recentItemActive : ''}`}
            >
              <Stack direction='row' spacing={1} alignItems='center'>
                <FiberManualRecordIcon
                  fontSize='small'
                  sx={{ color: i === currentWinner ? 'rgba(13, 239, 164, 0.91)' : 'gray' }}
                  className={currentWinner ? classes.pulseText : ''}
                />
                <Typography style={{ fontSize: '1rem', fontWeight: '700' }} color='white' variant='body2'>
                  {winner?.username || 'Anonymous'}
                </Typography>
                <Typography style={{ fontSize: '0.75rem', fontWeight: '500' }} variant='caption' color='gray'>
                  • {winner?.providerName || 'Unknown Game'}
                </Typography>
              </Stack>
              <Stack direction='row' spacing={1} alignItems='center'>
                <Typography variant='body2' fontWeight='bold' color='success.main'>
                  {winner?.winAmount?.toLocaleString() || '0'}
                  {winner?.isScActive ? ' SC' : ' GC'}
                </Typography>
              </Stack>
            </Box>
          ))}
        </Stack>
      </Box>

      {/* Stats */}
      <Grid container spacing={{ xs: 1, sm: 2 }}>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography color='success.main' fontWeight='bold' variant='h6'>
              $47K+
            </Typography>
            <Typography style={{ fontSize: '0.75rem', fontWeight: '500' }} variant='caption' color='gray'>
              Won Today
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography color='primary' fontWeight='bold' variant='h6'>
              156
            </Typography>
            <Typography style={{ fontSize: '0.75rem', fontWeight: '500' }} variant='caption' color='gray'>
              Winners Today
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography sx={{ color: '#60a5fa' }} fontWeight='bold' variant='h6'>
              $2.1M
            </Typography>
            <Typography style={{ fontSize: '0.75rem', fontWeight: '500' }} variant='caption' color='gray'>
              This Month
            </Typography>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default LiveWinners
