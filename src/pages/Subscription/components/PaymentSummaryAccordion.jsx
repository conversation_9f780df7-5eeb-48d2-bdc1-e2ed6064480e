import React from 'react'
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { formatPriceWithCommas } from '../../../utils/helpers'
import usdCash from '../../../components/ui-kit/icons/utils/usd-cash.webp'
import cardCoin2 from '../../../components/ui-kit/icons/utils/card-coin2.webp'

const PurchaseSummaryAccordion = ({ yearly, packageDetails, finalAmount, refundAmount, isExclusiveBonusClaimed, exclusiveBonus }) => {
  let remainingAmount = 0
  if (refundAmount > 0) {
    if (yearly) {
      remainingAmount = packageDetails?.yearlyAmount - refundAmount
    } else {
      remainingAmount = packageDetails?.monthlyAmount - refundAmount
    }
  }
  return (
    <Accordion className='payment-accordion'>
      <AccordionSummary
        className='payment-accordion-summary'
        expandIcon={<ArrowDropDownIcon sx={{ color: 'white !important' }} />}
        aria-controls='panel1-content'
        id='panel1-header'
      >
        <Typography className='payment-accordion-summary-title' component='p'>
          Purchase Summary
        </Typography>
      </AccordionSummary>
      <AccordionDetails className='payment-accordion-details'>
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>{packageDetails?.name} {yearly ? '12' : '1'} month plan price</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <Typography>
              {yearly ? `$${formatPriceWithCommas(packageDetails?.yearlyAmount?.toFixed(2))}` : `$${formatPriceWithCommas(packageDetails?.monthlyAmount?.toFixed(2))}`}
            </Typography>
          </Box>
        </Box>
        {refundAmount > 0 && (
          <>
            <Box className='payment-accordion-detail-item'>
              <Typography className='payment-accordion-detail-item-title' component='p'>Remaining amount from current plan</Typography>
              <Box className='payment-accordion-detail-item-content'>
                - ${formatPriceWithCommas((remainingAmount).toFixed(2))}
              </Box>
            </Box>
          </>
        )}
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>Bonus SC Coin</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <img src={usdCash} alt='Coin' className='payment-accordion-detail-item-img' />
            <Typography>
              {formatPriceWithCommas(packageDetails?.scCoin || 0)}{' '}
            </Typography>
          </Box>
        </Box>
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>Bonus GC Coin</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <img src={cardCoin2} alt='Coin' className='payment-accordion-detail-item-img' />
            <Typography>
              {formatPriceWithCommas(packageDetails?.gcCoin || 0)}{' '}
            </Typography>
          </Box>
        </Box>
        {isExclusiveBonusClaimed && (
          <>
            {exclusiveBonus?.bonusSc > 0 && (
              <Box className='payment-accordion-detail-item'>
                <Typography className='payment-accordion-detail-item-title-bonus' component='p'>Exclusive Bonus SC Coin</Typography>
                <Box className='payment-accordion-detail-item-content-bonus'>
                  <img src={usdCash} alt='Coin' className='payment-accordion-detail-item-img' />
                  <Typography>
                    {formatPriceWithCommas(exclusiveBonus?.bonusSc || 0)}{' '}
                  </Typography>
                </Box>
              </Box>
            )}
            {exclusiveBonus?.bonusGc > 0 && (
              <Box className='payment-accordion-detail-item'>
                <Typography className='payment-accordion-detail-item-title-bonus' component='p'>Exclusive Bonus GC Coin</Typography>
                <Box className='payment-accordion-detail-item-content-bonus'>
                  <img src={cardCoin2} alt='Coin' className='payment-accordion-detail-item-img' />
                  <Typography>
                    {formatPriceWithCommas(exclusiveBonus?.bonusGc || 0)}{' '}
                  </Typography>
                </Box>
              </Box>
            )}
          </>
        )}
        <Box className='payment-accordion-detail-item total-amount'>
          <Typography sx={{ color: 'white !important', fontWeight: '600' }} component='p'>Total Amount</Typography>
          <Typography>
            <b>${formatPriceWithCommas(finalAmount?.toFixed(2))}</b>
          </Typography>
        </Box>
      </AccordionDetails>
    </Accordion>
  )
}

export default PurchaseSummaryAccordion
