import React from 'react'
import { Box, Grid, Paper } from '@mui/material'
import { makeStyles } from '@mui/styles'
import PaidTwoToneIcon from '@mui/icons-material/PaidTwoTone'
import SportsEsportsOutlinedIcon from '@mui/icons-material/SportsEsportsOutlined'

const useStyles = makeStyles((theme) => ({
  section: {
    backgroundColor: '#1e1e1e',
    padding: theme.spacing(4, 2),
    textAlign: 'center'
  },
  title: {
    color: '#fff',
    marginBottom: theme.spacing(2),
    fontWeight: '700'
  },
  featureCard: {
    padding: theme.spacing(3),
    background: 'transparent',
    boxShadow: 'none',
    [theme.breakpoints.down('md')]: {
      margin: '0 !important'
    }
  },
  iconWrapper: {
    margin: '0 auto',
    marginBottom: theme.spacing(2),
    backgroundColor: theme.colors.YellowishOrange,
    width: 64,
    height: 64,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%'
  },
  featureTitle: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: theme.spacing(1)
  },
  featureText: {
    color: '#ccc',
    fontWeight: 500
  },
  urgencySection: {
    background: theme.colors.YellowishOrange,
    padding: theme.spacing(2, 2),
    textAlign: 'center'
  },
  urgencyText: {
    color: '#1e1e1e',
    fontWeight: 'bold',
    fontSize: '1.25rem'
  },
  spotBox: {
    padding: theme.spacing(1.5),
    borderRadius: 8,
    fontWeight: 'bold',
    backgroundColor: '#1e1e1e',
    color: theme.colors.YellowishOrange,
    minWidth: 72
  },
  goldSpotBox: {
    color: theme.colors.YellowishOrange,
    animation: 'pulse 1.5s infinite'
  },
  availabilityNote: {
    marginTop: theme.spacing(2),
    fontWeight: 'bold',
    fontSize: '1rem'
  }
}))

const ValueUrgencySections = () => {
  const classes = useStyles()
  const spotsLeft = {
    bronze: 34,
    silver: 12,
    gold: 3
  }

  return (
    <>
      <Box className={classes.section}>
        <h2 className={classes.title}>Why Subscribe ?</h2>
        <Grid container spacing={{ xs: 1, sm: 4 }} justifyContent='center'>
          {[
            {
              icon: <PaidTwoToneIcon />,
              title: 'Maximum Value',
              text: 'Get more coins and sweepstakes entries than any one-time purchase'
            },
            {
              icon: <SportsEsportsOutlinedIcon />,
              title: 'Exclusive Access',
              text: 'Play games and join tournaments only available to subscribers'
            },
            {
              icon: <SportsEsportsOutlinedIcon />,
              title: 'VIP Treatment',
              text: 'Priority support, exclusive badges, and special recognition'
            }
          ].map((item, idx) => (
            <Grid item xs={12} md={4} key={idx}>
              <Paper className={classes.featureCard}>
                <Box className={classes.iconWrapper}>{item.icon}</Box>
                <p className={classes.featureTitle}>{item.title}</p>
                <p className={classes.featureText}>{item.text}</p>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </>
  )
}

export default ValueUrgencySections
