import React from 'react'
import { makeStyles } from '@mui/styles'
import { Box, Container, Grid, Avatar, Stack, IconButton } from '@mui/material'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
import StarIcon from '@mui/icons-material/Star'
import PeopleIcon from '@mui/icons-material/People'
import FormatQuoteOutlinedIcon from '@mui/icons-material/FormatQuoteOutlined'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#132B27'
  },
  sectionTitle: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: theme.spacing(2),
    fontSize: '1.25rem',
    [theme.breakpoints.up('sm')]: {
      fontSize: '1.5rem'
    },
    [theme.breakpoints.up('md')]: {
      fontSize: '2rem'
    }
  },
  sectionTitle2: {
    color: theme.colors.YellowishOrange,
    fontWeight: 'bold',
    fontSize: '1.25rem',
    [theme.breakpoints.up('sm')]: {
      fontSize: '1.5rem'
    },
    [theme.breakpoints.up('md')]: {
      fontSize: '2rem'
    }
  },
  statsRow: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing(2),
    color: theme.colors.YellowishOrange
  },
  statBox: {
    textAlign: 'center'
  },
  swiperCard: {
    backgroundColor: '#0F0D11',
    borderRadius: theme.shape.borderRadius,
    border: '1px solid #FDB72E33',
    padding: theme.spacing(2),
    transition: 'all 0.3s',
    '&:hover': {
      borderColor: theme.colors.YellowishOrange,
      transform: 'scale(1)'
    }
  },
  testimonialSwiper: {
    '& .swiper-pagination': {
      bottom: '-5 !importnat'
    }
  },

  testimonialText: {
    color: '#ccc',
    fontSize: '0.9rem',
    lineHeight: 1.5,
    fontWeight: 500
  },
  badge: {
    backgroundColor: '#FDB72E1A',
    border: '1px solid #FDB72E4D',
    borderRadius: 9999,
    padding: theme.spacing(0.5, 2),
    display: 'inline-block',
    fontSize: '0.75rem',
    fontWeight: 'bold',
    color: theme.colors.YellowishOrange
  },
  userName: {
    color: '#fff',
    fontWeight: 600,
    fontSize: '0.9rem'
  },
  userLocation: {
    color: '#aaa',
    fontSize: '0.75rem'
  },
  navButton: {
    backgroundColor: 'rgba(253, 183, 46, 0.7) !important',
    color: `${theme.colors.YellowishOrange} !important`,
    opacity: '0.7',
    borderRadius: '50%',
    padding: theme.spacing(1),
    transition: 'all 0.2s',
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  trustBox: {
    backgroundColor: 'rgb(27 24 30 / 0.5)',
    border: '1px solid #FDB72E1A',
    borderRadius: '0.5rem',
    textAlign: 'center',
    padding: theme.spacing(2),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    }
  },
  trustTitle: {
    color: theme.colors.YellowishOrange,
    fontWeight: 'bold',
    fontSize: '1.5rem !important'
  },
  trustSubtitle: {
    color: '#aaa',
    fontSize: '1rem !important'
  }
}))

const SocialProof = () => {
  const classes = useStyles()

  // Demo Testimonials
  const testimonialsData = [
    {
      name: 'Emily R.',
      location: 'Los Angeles, CA',
      text: 'This platform changed the way I play! The bonuses and rewards are just unbeatable.',
      highlight: 'Best Rewards Ever',
      rating: 5,
      tier: 'Gold Member',
      image: 'https://randomuser.me/api/portraits/women/65.jpg'
    },
    {
      name: 'James T.',
      location: 'New York, NY',
      text: 'I’ve been a subscriber for over a year and it’s the best gaming experience out there.',
      highlight: '1 Year Loyalty',
      rating: 5,
      tier: 'Premium Member',
      image: 'https://randomuser.me/api/portraits/men/45.jpg'
    },
    {
      name: 'Sophia M.',
      location: 'Miami, FL',
      text: 'The customer support is outstanding. They helped me instantly when I had a question!',
      highlight: 'Amazing Support',
      rating: 5,
      tier: 'Basic Member',
      image: 'https://randomuser.me/api/portraits/women/22.jpg'
    },
    {
      name: 'Liam K.',
      location: 'Chicago, IL',
      text: 'Winning feels so much better with the exclusive features I got as a subscriber!',
      highlight: 'Big Wins',
      rating: 4,
      tier: 'Gold Member',
      image: 'https://randomuser.me/api/portraits/men/33.jpg'
    },
    {
      name: 'Olivia P.',
      location: 'Seattle, WA',
      text: 'Love the variety of games and the fact I can cancel anytime. Super flexible and fun!',
      highlight: 'Flexibility',
      rating: 5,
      tier: 'Basic Member',
      image: 'https://randomuser.me/api/portraits/women/12.jpg'
    },
    {
      name: 'Noah S.',
      location: 'Austin, TX',
      text: 'Easiest subscription I’ve ever had. Smooth, secure, and so many benefits.',
      highlight: 'Easy & Secure',
      rating: 5,
      tier: 'Premium Member',
      image: 'https://randomuser.me/api/portraits/men/28.jpg'
    }
  ]

  return (
    <Box className={classes.root}>
      <Container>
        <Box textAlign='center' mb={4} paddingTop={4}>
          <p className={classes.sectionTitle}>Join 50,000+ Happy Subscribers</p>
          <Box className={classes.statsRow}>
            <Box className={classes.statBox}>
              <p className={classes.sectionTitle2}>4.9/5</p>
              <StarIcon />
              <StarIcon />
              <StarIcon />
              <StarIcon />
              <StarIcon />
              <p style={{ color: '#fff' }} variant='caption'>App Store Rating</p>
            </Box>
            <Box className={classes.statBox}>
              <p className={classes.sectionTitle2}>50K+</p>
              <p style={{ color: '#fff' }} variant='caption'>Active Subscribers</p>
            </Box>
            <Box className={classes.statBox}>
              <p className={classes.sectionTitle2}>$2M+</p>
              <p style={{ color: '#fff' }} variant='caption'>Prizes Won</p>
            </Box>
          </Box>
        </Box>

        <Box position='relative' mb={2}>
          <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            spaceBetween={20}
            className={classes.testimonialSwiper}
            slidesPerView={1}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            navigation={{ nextEl: '.testimonial-next', prevEl: '.testimonial-prev' }}
            breakpoints={{
              640: { slidesPerView: 2, spaceBetween: 20 },
              1024: { slidesPerView: 3, spaceBetween: 30 }
            }}
          >
            {testimonialsData.map((testimonial, index) => (
              <SwiperSlide key={index}>
                <Box className={classes.swiperCard}>
                  <Box display='flex' justifyContent='space-between' mb={2}>
                    <FormatQuoteOutlinedIcon style={{ width: 30, height: 30, color: '#FDB72E' }} />
                    <Box className={classes.badge}>{testimonial.tier}</Box>
                  </Box>
                  <p className={classes.testimonialText} mb={2}>
                    '{testimonial.text}'
                  </p>
                  <Box className={classes.badge} mb={2}>
                    ✨ {testimonial.highlight}
                  </Box>
                  <Stack direction='row' spacing={1} alignItems='center'>
                    <Avatar src={testimonial.image} alt={testimonial.name} />
                    <Box>
                      <p className={classes.userName}>{testimonial.name}</p>
                      <p className={classes.userLocation}>{testimonial.location}</p>
                    </Box>
                    <p>
                      {[...Array(testimonial.rating)].map((_, key) => (
                        <StarIcon key={key} style={{ color: '#FDB72E' }} />
                      ))}
                    </p>
                  </Stack>
                </Box>
              </SwiperSlide>
            ))}
          </Swiper>

          <Stack direction='row' justifyContent='center' alignItems='center' spacing={2} mt={2}>
            <IconButton className={`testimonial-prev ${classes.navButton}`}>
              <ArrowBackIosNewIcon fontSize='small' />
            </IconButton>
            <Stack direction='row' alignItems='center' spacing={1}>
              <PeopleIcon style={{ width: 20, height: 20, color: '#FDB72E' }} />
              <p style={{ color: '#FDB72E', fontWeight: '700' }}>{testimonialsData.length} verified reviews</p>
            </Stack>
            <IconButton className={`testimonial-next ${classes.navButton}`}>
              <ArrowForwardIosIcon fontSize='small' />
            </IconButton>
          </Stack>
        </Box>

        <Grid container spacing={{ xs: 1, sm: 2 }} mt={1} pb={2}>
          <Grid item xs={6} md={3}>
            <Box className={classes.trustBox}>
              <p className={classes.trustTitle}>98%</p>
              <p className={classes.trustSubtitle}>Satisfaction Rate</p>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box className={classes.trustBox}>
              <p className={classes.trustTitle}>24/7</p>
              <p className={classes.trustSubtitle}>Support Available</p>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box className={classes.trustBox}>
              <p className={classes.trustTitle}>1M+</p>
              <p className={classes.trustSubtitle}>Games Played</p>
            </Box>
          </Grid>
          <Grid item xs={6} md={3}>
            <Box className={classes.trustBox}>
              <p className={classes.trustTitle}>$5M+</p>
              <p className={classes.trustSubtitle}>Total Winnings</p>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default SocialProof
