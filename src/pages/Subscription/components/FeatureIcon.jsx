import React from 'react'
import { Box } from '@mui/material'

const FeatureIcon = ({ icon: IconComponent }) => {
  if (!IconComponent) return null

  return (
    <Box
      component='span'
      sx={{
        color: '#FDB72E',
        fontSize: 16,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: '5px'
      }}
    >
      <IconComponent fontSize='inherit' />
    </Box>
  )
}

export default FeatureIcon
