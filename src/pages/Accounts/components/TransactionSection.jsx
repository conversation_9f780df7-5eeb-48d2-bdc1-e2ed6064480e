import React, { useState } from 'react'
import useStyles from '../bets.styles'
import '../../../../src/App.css'
import {
  Grid,
  Pagination,
  Typography,
  Tooltip,
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHead,
  TableContainer,
  useTheme
} from '@mui/material'
import useTransaction from '../hooks/useTransactionSection'
import { usePortalStore } from '../../../store/userPortalSlice'
import { capitalizeText, commonDateTimeFormat, formatCommonNumber } from '../../../utils/helpers'
import moment from 'moment'
import { TransitionLeaderBoard } from '../../Lobby/components/LeaderBoard/TransitionLeaderBoard.style'
import { PaymentQuery } from '../../../reactQuery'
import SomethingWentWrong from '../../../components/SomethingWentWrong'
import EditIcon from '../../../components/ui-kit/icons/svg/editicon.svg'
import { toast } from 'react-hot-toast'
import EditCancelRedeem from './components/EditCancelRedeem'
import Select from 'react-select'
import DatePicker from '../../../components/DatePicker'
import Delete from '@mui/icons-material/Delete'
/* eslint-disable multiline-ternary */
const options = [
  { value: 'bonus', label: 'Awarded Gifts' },
  { value: 'deposit', label: 'Purchase' },
  { value: 'redeem', label: 'Redemption' },
  { value: 'vault', label: 'Vault' },
  { value: 'tournament', label: 'Tournaments' },
  { value: 'jackpot', label: 'Jackpot' },
  { value: 'freeSpin', label: 'Free Spin' },
  { value: 'subscription', label: 'TMF+ Subscriptions' }
]
const statusOptions = [
  { value: 'pending', label: 'Pending' },
  { value: 'success', label: 'Success' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'inprogress', label: 'In Progress' },
  { value: 'all', label: 'All' }
]

export const transactionStatus = {
  '-1': 'Initiated',
  0: 'Pending',
  1: 'Success',
  2: 'Cancelled',
  3: 'Failed',
  4: 'Rollback',
  5: 'Approved',
  6: 'Declined',
  7: 'Pending',
  8: 'Pending',
  9: 'Void',
  10: 'Refund'
}

const TransactionSection = () => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [selectedOption, setSelectedOption] = useState({ value: 'bonus', label: 'Awarded Gifts' })
  const theme = useTheme()

  const handleChange = (event, value) => {
    setPageNo(value)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const {
    transactionsData,
    isTransactionLoading,
    pageNo,
    setPageNo,
    limit,
    setStartDate,
    setEndDate,
    actionType,
    setActionType,
    refetch,
    selectedStatus,
    setSelectedStatus
  } = useTransaction()
  const successToggler = (res) => {
    toast.success(res?.message)
    refetch()
    portalStore.closePortal()
  }

  const errorToggler = () => {
    portalStore.openPortal(() => <SomethingWentWrong />, 'innerModal')
  }
  const cancelRedeemRequest = PaymentQuery.useCancelRedeemMutation({ successToggler, errorToggler })
  const confirmRedeemRequest = PaymentQuery.useConfirmRedeemMutation({ successToggler, errorToggler })

  const handleActionChange = (event) => {
    setPageNo(1)
    setSelectedOption(event)
    setActionType(event.value)
  }

  const handleStatusChange = (event) => {
    setPageNo(1)
    setSelectedStatus(event)
  }

  const handleEdit = (item) => {
    portalStore.openPortal(
      () => (
        <EditCancelRedeem
          item={item}
          cancelRedeemRequest={cancelRedeemRequest}
          confirmRedeemRequest={confirmRedeemRequest}
          type='redeemPage'
        />
      ),
      'withdrawModal'
    )
  }

  const handleCancelRedeem = (transactionId) => {
    cancelRedeemRequest.mutate({ transactionId })
  }

  const handleStartDateChange = (date) => {
    setStartDate(date)
  }

  // Function to handle end date change
  const handleEndDateChange = (date) => {
    setEndDate(date)
  }

  return (
    <>
      <TransitionLeaderBoard theme={theme}>
        <Grid>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={4} className={classes.transactionInputs}>
              <Grid className={classes.authInputWrap}>
                <DatePicker onStartDateChange={handleStartDateChange} onEndDateChange={handleEndDateChange} />
              </Grid>
            </Grid>
            <Grid item xs={12} sm={4} className={classes.transactionInputs}>
              <Grid className={classes.authInputWrap}>
                <Select
                  value={selectedOption} // defaultMenuIsOpen
                  onChange={handleActionChange}
                  options={options}
                  className={classes.reactCoinSelect}
                  classNamePrefix='reactInnerCoinSelect'
                  placeholder='Action Type'
                />
              </Grid>
            </Grid>

            <Grid item xs={12} sm={4} className={classes.transactionInputs}>
              {selectedOption.value === 'redeem' && (
                <Grid className={classes.authInputWrap}>
                  <Select
                    value={selectedStatus} // defaultMenuIsOpen
                    onChange={handleStatusChange}
                    options={statusOptions}
                    className={classes.reactCoinSelect}
                    classNamePrefix='reactInnerCoinSelect'
                    placeholder='Status'
                  />
                </Grid>
              )}
            </Grid>
          </Grid>
          <TableContainer style={{ margin: '20px 0px' }}>
            <Grid className='leaderBoardContainer'>
              <Grid className={classes.tableCard}>
                {isTransactionLoading ? (
                  <Typography colSpan={8} style={{ textAlign: 'center' }}>
                    <CircularProgress size={24} />
                  </Typography>
                ) : transactionsData?.rows?.length ? (
                  transactionsData?.rows?.map((item, index) => {
                    return (
                      <>
                        <Grid className={classes.tableCardHead}>
                          <Typography className='tdate'>
                            {moment(new Date(item?.updated_at)).format(commonDateTimeFormat.date)}
                          </Typography>
                          <Typography className='tstatus'>
                            <span>{transactionStatus[item?.status]}</span>
                          </Typography>
                        </Grid>
                        <Grid className={classes.tableCardBody}>
                          <Grid>
                            <Typography>Amount</Typography>
                            <Typography>
                              <b>{formatCommonNumber(item?.amount)}</b>
                            </Typography>
                          </Grid>

                          <Grid>
                            <Typography>Gold Coins</Typography>
                            <Typography>
                              <b>{formatCommonNumber(item?.gccoin)}</b>
                            </Typography>
                          </Grid>

                          <Grid>
                            <Typography>Sweep Coins</Typography>
                            <Typography>
                              <b>{formatCommonNumber(item?.sccoin)}</b>
                            </Typography>
                          </Grid>

                          <Grid>
                            <Typography>Type</Typography>
                            <Typography>
                              <b>{item?.transactiontype != null ? capitalizeText(item?.transactiontype) : '-'}</b>
                            </Typography>
                          </Grid>
                          {item?.transactiontype === 'Redemption' ? (
                            item?.status === 0 ? (
                              <Grid>
                                <Button onClick={() => handleEdit(item)}>
                                  <img src={EditIcon} />
                                </Button>
                              </Grid>
                            ) : (
                              <Grid>-</Grid>
                            )
                          ) : null}
                        </Grid>
                      </>
                    )
                  })
                ) : (
                  <Typography colSpan={8} style={{ textAlign: 'center', color: 'red' }}>
                    <span>No Records Found</span>
                  </Typography>
                )}
              </Grid>

              <Table className={classes.transactionTable}>
                <TableHead>
                  <TableRow>
                    {actionType !== 'bonus' &&
                      actionType !== 'tournament' &&
                      actionType !== 'jackpot' &&
                      actionType !== 'freeSpin' && (
                        <TableCell style={{ flex: 1, paddingLeft: '2rem' }}>Transaction ID</TableCell>)}
                    {actionType !== 'freeSpin' && <TableCell style={{ flex: 1 }}>Date</TableCell>}
                    {actionType !== 'freeSpin' && <TableCell style={{ flex: 1 }}>Time</TableCell>}
                    {(actionType === 'deposit' || actionType === 'redeem' || actionType === 'subscription') && (
                      <TableCell style={{ flex: 1 }}>Amount</TableCell>
                    )}
                    {actionType === 'freeSpin' && <TableCell style={{ flex: 1 }}>Game</TableCell>}
                    {actionType === 'freeSpin' && <TableCell style={{ flex: 1 }}>Bet Amount</TableCell>}
                    {actionType === 'freeSpin' && <TableCell style={{ flex: 1 }}>Round </TableCell>}
                    {actionType !== 'redeem' && actionType !== 'freeSpin' && <TableCell style={{ flex: 1 }}>Gold Coins</TableCell>}
                    {actionType !== 'redeem' && actionType !== 'freeSpin' && <TableCell style={{ flex: 1 }}>Sweep Coins</TableCell>}
                    {/* {actionType === 'subscription' && <TableCell style={{ flex: 1 }}>Plan Type</TableCell>} */}
                    {actionType === 'freeSpin' && <TableCell style={{ flex: 1 }}>Win Gold Coins</TableCell>}
                    {actionType === 'freeSpin' && <TableCell style={{ flex: 1 }}> Win Sweep Coins</TableCell>}
                    {actionType === 'tournament' && <TableCell style={{ flex: 1 }}>Flag</TableCell>}
                    {actionType === 'subscription' && <TableCell style={{ flex: 1 }}>Plan Type</TableCell>}
                    <TableCell style={{ flex: 1 }}>Status</TableCell>

                    {actionType === 'bonus' && <TableCell style={{ flex: 1 }}>Bonus Type</TableCell>}
                    {actionType === 'redeem' && <TableCell style={{ flex: 1, textAlign: 'center' }}>Edit</TableCell>}
                    {actionType === 'redeem' && (
                      <TableCell style={{ flex: 1, textAlign: 'center' }}>Cancel Request</TableCell>
                    )}

                    {(actionType === 'vault' ||
                      actionType === 'jackpot') && <TableCell style={{ flex: 1 }}>Transaction Type</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isTransactionLoading ? (
                    <TableRow style={{ background: 'transparent' }}>
                      <TableCell colSpan={8} style={{ textAlign: 'center' }}>
                        <CircularProgress size={24} />
                      </TableCell>
                    </TableRow>
                  ) : transactionsData?.rows?.length ? (
                    transactionsData?.rows?.map((item, index) => {
                      return (
                        <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                          {(item?.transactiontype === 'Redemption' ||
                            item?.transactiontype === 'Purchase' ||
                            item?.transactiontype === 'Vault Withdraw' ||
                            item?.transactiontype === 'Vault Deposit' ||
                            item?.transactiontype === 'TMF Plus Membership') && (
                            <TableCell style={{ flex: 1, paddingLeft: '2rem' }} scope='row'>
                              <span>{item?.transactionId || item?.transactionid || '-'}</span>
                            </TableCell>
                          )}
                          {actionType === 'freeSpin' && (
                            <TableCell style={{ flex: 1, paddingLeft: '2rem' }} scope='row'>
                              <span>{item?.gameName || '-'}</span>
                            </TableCell>
                          )}
                          {actionType === 'freeSpin' && (
                            <TableCell style={{ flex: 1, paddingLeft: '2rem' }} scope='row'>
                              <span>{item?.freeSpinAmountPerBet || '-'}</span>
                            </TableCell>
                          )}
                          {actionType === 'freeSpin' && (
                            <TableCell style={{ flex: 1, paddingLeft: '2rem' }} scope='row'>
                              <span>{item?.freeSpinRound || '-'}</span>
                            </TableCell>
                          )}

                          {actionType !== 'freeSpin' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{moment(new Date(item?.updated_at)).format(commonDateTimeFormat.date)}</span>
                            </TableCell>
                          )}
                          {actionType !== 'freeSpin' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{new Date(item?.updated_at).toLocaleTimeString('en-US', { hour12: true })}</span>
                            </TableCell>
                          )}
                          {(item?.transactiontype === 'Redemption' || item?.transactiontype === 'Purchase' || item?.transactiontype === 'TMF Plus Membership') && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>${formatCommonNumber(item?.amount)}</span>
                            </TableCell>
                          )}
                          {(item?.transactiontype !== 'Redemption' ||
                            item?.transactiontype === 'Vault Withdraw' ||
                            item?.transactiontype === 'Vault Deposit' ||
                            item?.transactiontype === 'Purchase') && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.gccoin ? formatCommonNumber(item?.gccoin) : 0}</span>
                            </TableCell>
                          )}

                          {(item?.transactiontype !== 'Redemption' ||
                            item?.transactiontype === 'Vault Withdraw' ||
                            item?.transactiontype === 'Vault Deposit' ||
                            item?.transactiontype === 'Purchase') && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.sccoin ? formatCommonNumber(item?.sccoin) : 0}</span>
                            </TableCell>
                          )}
                          {actionType === 'subscription' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.redeememail}</span>
                            </TableCell>
                          )}
                          {actionType === 'freeSpin' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.status || '-'}</span>
                            </TableCell>
                          )}
                          {actionType === 'tournament' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.flag}</span>
                            </TableCell>
                          )}
                          {actionType !== 'freeSpin' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{transactionStatus[item?.status]}</span>
                            </TableCell>
                          )}
                          {actionType === 'bonus' ||
                            ((item?.transactiontype === 'Vault Withdraw' ||
                              item?.transactiontype === 'Vault Deposit') && (
                              <TableCell style={{ flex: 1 }} scope='row'>
                                <span>
                                  {item?.transactiontype != null ? capitalizeText(item?.transactiontype) : '-'}
                                </span>
                              </TableCell>
                            ))}
                          {actionType === 'bonus' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.transactiontype}</span>
                            </TableCell>
                          )}
                          {actionType === 'jackpot' && (
                            <TableCell style={{ flex: 1 }} scope='row'>
                              <span>{item?.transactiontype}</span>
                            </TableCell>
                          )}
                          {item?.transactiontype === 'Redemption' ? (
                            item?.status === 0 ? (
                              <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                <Tooltip title='Edit Request' arrow>
                                  <Button onClick={() => handleEdit(item)}>
                                    <img src={EditIcon} />
                                  </Button>
                                </Tooltip>
                              </TableCell>
                            ) : (
                              <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                -
                              </TableCell>
                            )
                          ) : null}
                          {item?.transactiontype === 'Redemption' ? (
                            item?.status === 0 ? (
                              <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                <Tooltip title='Cancel Request' arrow>
                                  <Button
                                    onClick={() => handleCancelRedeem(item?.transactionid)}
                                    style={{ color: '#fff', textDecoration: 'underline' }}
                                  >
                                    <Delete />
                                  </Button>
                                </Tooltip>
                              </TableCell>
                            ) : (
                              <TableCell style={{ flex: 1, textAlign: 'center' }} scope='row'>
                                -
                              </TableCell>
                            )
                          ) : null}
                        </TableRow>
                      )
                    })
                  ) : (
                    <TableRow style={{ background: 'transparent' }}>
                      <TableCell colSpan={8} style={{ textAlign: 'center', color: 'red' }}>
                        <span>No Records Found</span>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Grid>
          </TableContainer>
        </Grid>
      </TransitionLeaderBoard>

      {transactionsData?.count > 0 && !isTransactionLoading && (
        <Pagination
          count={Math.ceil(transactionsData?.count / limit)}
          page={pageNo}
          className={classes.tablePagination}
          onChange={handleChange}
          defaultPage={3}
          siblingCount={1}
        />
      )}
    </>
  )
}

export default TransactionSection
