import React, { useEffect, lazy, Suspense } from 'react'
import useStyles from './Lobby.styles'
import '../../../src/App.css'
import { Grid } from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { useGetProfileMutation, useVerifyEmailMutation } from '../../reactQuery'
import { useUserStore } from '../../store/useUserSlice'
import { usePortalStore } from '../../store/userPortalSlice'

import { getLoginToken } from '../../utils/storageUtils'
import { getInitialVisitorID, updateSDKPageVisit } from '../../utils/optimoveHelper'

import LandingPage from '../LandingPage'
import { getVipRoute } from '../../utils/cookiesCollection'

const UserNameModal = lazy(() => import('../../components/Modal/Signup/UserNameModal'))
const Signup = lazy(() => import('../../components/Modal/Signup'))
const Signin = lazy(() => import('../../components/Modal/Signin'))
const VerifyForgotPassword = lazy(() => import('../ForgotPassword/VerifyForgotPassword'))
const PaymentStatus = lazy(() => import('../../components/PaymentStatus'))

const Lobby = () => {
  const classes = useStyles()
  const navigate = useNavigate()
  const location = useLocation()
  const currentUrl = window.location.origin + location.pathname + location.search
  const portalStore = usePortalStore()
  const { userDetails, isAuthenticate, setUserDetails, setIsAuthenticate, logout } = useUserStore()

  const searchParams = new URLSearchParams(location.search)
  const vipRoute = getVipRoute('vipRoute')
 
  // Affiliate tracking
  useEffect(() => {
    const affiliateCode = searchParams.get('btag') || ''
    const affiliateId = searchParams.get('affid') || ''
    const affiliatePromocode = searchParams.get('promocode') || ''
    const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString()

    if (affiliateCode || affiliateId || affiliatePromocode) {
      document.cookie = `affiliateCode=${affiliateCode}; expires=${expires}`
      document.cookie = `affiliateId=${affiliateId}; expires=${expires}`
      document.cookie = `affiliatePromocode=${affiliatePromocode}; expires=${expires}`
    }
  }, [])

  const handleForgotPasswordClose = () => {
    portalStore.closePortal()
    navigate('/')
  }

  // Initial visitor tracking and modals (forgot password, verify email)
  useEffect(() => {
    getInitialVisitorID()
    const token = searchParams.get('token')
    const pathSegments = location.pathname.split('/')

    if (token) {
      if (pathSegments.includes('verifyEmail')) {
        verifyEmailMutation.mutate({ token })
      }
      if (pathSegments.includes('forgotPassword')) {
        if (getLoginToken() || isAuthenticate) logout()
        portalStore.openPortal(
          () => (
            <Suspense fallback={<div>Loading forgot password...</div>}>
              <VerifyForgotPassword handleClose={handleForgotPasswordClose} />
            </Suspense>
          ),
          'loginModal'
        )
      }
    }

    const popup = searchParams.get('callpopup')
    const affiliatePopup = searchParams.get('lp')

    if ((popup === 'signup' || affiliatePopup) && !(getLoginToken() || isAuthenticate)) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<div>Loading signup...</div>}>
            <Signup />
          </Suspense>
        ),
        'signupModal'
      )
    } else if (popup === 'signin') {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<div>Loading signin...</div>}>
            <Signin />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }, [])

  // Username modal
  useEffect(() => {
    if (isAuthenticate && getLoginToken() === 'null' && userDetails?.username === null) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<div>Loading username modal...</div>}>
            <UserNameModal />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }, [isAuthenticate, userDetails])

  // Optimove page tracking
  useEffect(() => {
    if (userDetails?.userId && import.meta.env.VITE_NODE_ENV === 'production') {
      updateSDKPageVisit(currentUrl, 'Lobby', null, userDetails.userId.toString())
    }
  }, [userDetails, currentUrl])

  useEffect(() => {
    if (vipRoute) {
      portalStore.openPortal(
        () => (
          <Suspense fallback={<div>Loading signin...</div>}>
            <Signin />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }, [vipRoute])
  // Payment handler
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.error('Profile fetch error:', error)
    }
  })

  const handlePaymentSuccess = (data) => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<div>Loading payment status...</div>}>
          <PaymentStatus paymentDetails={data} />
        </Suspense>
      ),
      'loginModal'
    )
  }

  useEffect(() => {
    const status = searchParams.get('status')
    if (['success', 'failed', 'cancelled'].includes(status)) {
      const data = {
        transactionId: searchParams.get('transactionId'),
        status,
        paymentMethod: searchParams.get('paymentMethod'),
        scCoin: searchParams.get('scCoin'),
        gcCoin: searchParams.get('gcCoin'),
        bonusSc: searchParams.get('bonusSc'),
        bonusGc: searchParams.get('bonusGc'),
        amount: searchParams.get('amount')
      }

      handlePaymentSuccess(data)

      if (status === 'success') {
        getProfileMutation.mutate()
      }
    }
  }, [location])

  // Email verification mutation
  const verifyEmailMutation = useVerifyEmailMutation({
    onSuccess: (res) => {
      const userData = res?.data?.user
      if (userData) {
        localStorage.setItem('username', userData.username)
        localStorage.setItem('phoneVerified', userData.phoneVerified)
        toast.success('Email Verified Successfully')
        setUserDetails(userData)
        setIsAuthenticate(true)
        navigate('/')
      }
    },
    onError: (err) => {
      const errors = err?.response?.data?.errors || []
      errors.forEach(({ description }) => {
        if (description) {
          // toast.error(description)
          navigate('/')
        }
      })
    }
  })

  return (
    <Grid className={classes.lobbyRight}>
      <LandingPage />
    </Grid>
  )
}

export default Lobby
