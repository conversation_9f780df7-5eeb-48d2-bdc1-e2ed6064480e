import React, { useState } from 'react'
import useStyles from '../Tournaments.styles'
import { useNavigate } from 'react-router-dom'
import { Box, Button, Grid } from '@mui/material'
import noData from '../../../components/ui-kit/icons/utils/no-data.webp'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'
import { formatDiscountAmount, formatPriceWithCommas } from '../../../utils/helpers'
import priceBadge from '../../../../src/components/ui-kit/icons/webp/card-badge.webp'
import defaultTournamentImage from '../../../components/ui-kit/icons/webp/tournament-4.webp'
import defaultVipTournamentImage from '../../../components/ui-kit/icons/webp/vip-card.webp'
import vipLock from '../../../components/ui-kit/icons/svg/vip-lock.svg'
import { usePortalStore, useUserStore } from '../../../store/store'
import CmsModal from '../../../components/CmsModal/CmsModal'
import toast from 'react-hot-toast'
import tournamentQuery from '../../../reactQuery/tournamentQuery'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'

/* eslint-disable multiline-ternary */
const TabTournament = ({ data, handleTournamentDetail, tabKey, coinType }) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)

  const { userDetails, setUserDetails } = useUserStore((state) => ({
    userDetails: state?.userDetails,
    setUserDetails: state?.setUserDetails
  }))
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser

  const [selectedTournamentInfo, setSelectedTournamentInfo] = useState({ tabKey: null, tournamentId: null })

  const navigate = useNavigate()

  const { isScTournamentTermsAccepted, isGcTournamentTermsAccepted } = userDetails || {}

  const formatDate = (dateString) => {
    const date = new Date(dateString)

    const options = { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true }
    const formattedDate = date.toLocaleString('en-US', options)

    return formattedDate.replace(',', '').replace('AM', ' AM').replace('PM', ' PM')
  }

  const handleJoinTournament = (pathname, entryCoin, tournamentId, tournamentData) => {
    const shouldOpenModal =
      (entryCoin === 'SC' && !isScTournamentTermsAccepted) || (entryCoin === 'GC' && !isGcTournamentTermsAccepted)

    if (shouldOpenModal) {
      portalStore.openPortal(() => <CmsModal path={pathname} handleConfirm={() => handleConfirm('submit', tournamentId, tournamentData)} />, 'cmsModal')
    } else {
      handleConfirm('submit', tournamentId, tournamentData)
    }
  }

  const handleConfirm = (data, tournamentId, tournamentData) => {
    if (data === 'submit' && tournamentId !== 0) {
      setSelectedTournamentInfo({ tabKey, tournamentId })
      const payload = {
        tournamentId: tournamentId,
        isTournamentTermsAccepted: true
      }
      mutationJoinTournament.mutate(payload)

      const isScTournamentTermsAccepted = tournamentData?.entryCoin === 'SC'
      const isGcTournamentTermsAccepted = tournamentData?.entryCoin === 'GC'

      setUserDetails({
        ...userDetails,
        isScTournamentTermsAccepted,
        isGcTournamentTermsAccepted
      })
      portalStore.closePortal()
    }
  }

  const successJoinToggler = (data) => {
    toast.success('Tournament Joined successfully.')
    navigate(`/tournament-detail/${selectedTournamentInfo.tabKey}/${selectedTournamentInfo.tournamentId}`)
  }

  const errorJoinToggler = () => { }
  const mutationJoinTournament = tournamentQuery.useJoinTournamentMutation({
    successJoinToggler,
    errorJoinToggler
  })

  return (
    <Box>
      <Grid container spacing={1}>
        {data && data.length > 0 ? (
          data.map((e) => {
            const isVIP = e?.vipTournament // `isVIP` indicates whether it's a VIP tournament
            const isUserAllowedForVIP = e?.isUserAllowedInVipTournament && isVIP // Check if the user is allowed for VIP tournaments
            const isSubscribed = userSubscription?.subscriptionDetail !== null
            const isSubscriberOnlyTournament = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_SUBSCRIBER_ONLY
            const tournamentDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_JOINING_FEE_DISCOUNT

            return (
              <Grid item xs={12} sm={6} md={6} lg={6} key={e?.tournamentId}>
                <Box
                  className={`${classes.tournamentItems} ${isVIP ? 'vip-tournament' : ''}`}
                  style={{
                    backgroundImage: isVIP
                      ? e?.imageUrl
                          ? `url(${e?.imageUrl})`
                          : `url(${defaultVipTournamentImage})`
                      : e?.imageUrl
                        ? `url(${e?.imageUrl})`
                        : `url(${defaultTournamentImage})`
                  }}
                  pointerEvents={isVIP && !isUserAllowedForVIP ? 'none' : 'auto'} // Disable interaction if the user is not allowed
                  onClick={() =>
                    isVIP && !isUserAllowedForVIP ? null : handleTournamentDetail(e?.tournamentId, tabKey)}
                >
                  {hasActiveSubscription && e?.isSubscriberOnly && <Box className='tmfBadge'> TMF Plus Exclusive </Box>}
                  {isVIP && !isUserAllowedForVIP && (
                    <Box className='vip-overlay'>
                      <Box className='vip-icon'>
                        <img src={vipLock} alt='lock' />
                      </Box>
                      {e?.vipTournamentTitle ? e?.vipTournamentTitle : 'Only VIP user can access.'}
                    </Box>
                  )}

                  <h3>{e?.title}</h3>
                  <Grid className='underline' />
                  <h4>
                    {formatDate(e?.startDate)} To {formatDate(e?.endDate)}
                  </h4>
                  <h4 className='pool-prize'>POOL PRIZE</h4>
                  {e?.entryCoin === 'SC'
                    ? e?.winSc > 0 && (
                      <Grid className='sc-badge'>
                        <img src={priceBadge} alt='price-badge' />
                        <span> {formatPriceWithCommas(e?.winSc)} SC</span>
                      </Grid>)
                    : e?.winGc > 0 && (
                      <Grid className='sc-badge'>
                        <img src={priceBadge} alt='price-badge' />
                        <span> {formatPriceWithCommas(e?.winGc)} GC</span>
                      </Grid>
                    )}
                  <h3 className='entry-fee'>
                    {e?.isJoined
                      ? 'Tournament Joined'
                      : e?.entryAmount === 0
                        ? 'Free Entry'
                        : (
                          <>
                            Entry Fee:{' '}
                            {hasActiveSubscription ? (
                              isSubscribed && tournamentDiscountPercentage ? (
                                <>
                                  <span className='original-price'>
                                    {formatPriceWithCommas(e?.entryAmount)} {coinType}
                                  </span>
                                  <span className='discounted-price'>
                                    {formatPriceWithCommas(
                                      formatDiscountAmount(e?.entryAmount, tournamentDiscountPercentage)
                                    )} {coinType}
                                  </span>
                                </>
                              ) : (
                                `${formatPriceWithCommas(e?.entryAmount)} ${coinType}`
                              )
                            ) : (
                              `${formatPriceWithCommas(e?.entryAmount)} ${coinType}`
                            )}

                          </>)}
                  </h3>
                  {e?.status !== '2' && e?.status !== '3' ? (
                    hasActiveSubscription && e?.isSubscriberOnly ? (
                      isSubscribed
                        ? isSubscriberOnlyTournament
                            ? (
                              <Button
                                className='btn btn-primary'
                                onClick={(event) => {
                                  event.stopPropagation()
                                  if (!e?.isJoined) {
                                    handleJoinTournament(
                                      e?.entryCoin === 'SC' ? '/cms/tournament-sc-terms' : '/cms/tournament-gc-terms',
                                      e?.entryCoin,
                                      e?.tournamentId,
                                      e
                                    )
                                  } else {
                                    handleTournamentDetail(e?.tournamentId, tabKey)
                                  }
                                }}
                              >
                                {e?.isJoined ? 'Play Now' : 'Join Now'}
                              </Button>)
                            : (
                              <Button
                                className='tmf-plus-btn btn btn-primary'
                                onClick={(event) => {
                                  event.stopPropagation()
                                  navigate('/subscriptions')
                                }}
                              >
                                <img src={vipLock} alt='lock' className='tmf-lock-icon' />
                                UPGRADE TMF PLUS
                              </Button>)
                        : (
                          <Button
                            className='tmf-plus-btn btn btn-primary'
                            onClick={(event) => {
                              event.stopPropagation()
                              navigate('/subscriptions')
                            }}
                          >
                            <img src={vipLock} alt='lock' className='tmf-lock-icon' />
                            JOIN TMF PLUS
                          </Button>)
                    ) : (
                      <Button
                        className='btn btn-primary'
                        onClick={(event) => {
                          event.stopPropagation()
                          if (!e?.isJoined) {
                            handleJoinTournament(
                              e?.entryCoin === 'SC' ? '/cms/tournament-sc-terms' : '/cms/tournament-gc-terms',
                              e?.entryCoin,
                              e?.tournamentId,
                              e
                            )
                          } else {
                            handleTournamentDetail(e?.tournamentId, tabKey)
                          }
                        }}
                      >
                        {e?.isJoined ? 'Play Now' : 'Join Now'}
                      </Button>
                    )
                  ) : (
                    <Box className='preview'>
                      <Button className='btn btn-secondary'>Preview</Button>
                    </Box>
                  )}
                </Box>
              </Grid>
            )
          })
        ) : (
          <Grid className={classes.NoData}>
            <img src={noData} alt='no-data-found' />
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default TabTournament
