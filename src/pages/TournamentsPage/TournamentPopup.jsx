import React from 'react'
import { <PERSON><PERSON>, DialogContent, Ty<PERSON>graphy, Button, IconButton, Box, Grid } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { Swiper, SwiperSlide } from 'swiper/react'
import useStyles from '../TournamentsPage/Tournaments.styles'
import { Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import priceBadge from '../../components/ui-kit/icons/webp/card-badge.webp'
import { formatDiscountAmount, formatPriceWithCommas } from '../../utils/helpers'
import vipLock from '../../components/ui-kit/icons/svg/vip-lock.svg'
import defaultVipTournamentImage from '../../components/ui-kit/icons/webp/vip-card.webp'
import defaultTournamentImage from '../../components/ui-kit/icons/webp/tournament-4.webp'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { Link, useNavigate } from 'react-router-dom'
import { usePortalStore } from '../../store/userPortalSlice'
import { useUserStore } from '../../store/useUserSlice'

const TournamentPopup = ({
  tournamentData,
  open,
  onClose,
  hanlePlayWithoutTournament = () => { },
  handlePlayInTournament = () => { },
  handleJoinTournament = () => { }
}) => {
  const portalStore = usePortalStore((state) => state)
  const classes = useStyles()
  const navigate = useNavigate()
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser
  const hasSubscription = userSubscription?.subscriptionDetail !== null
  const isSubscribed = userSubscription?.subscriptionDetail !== null
  const tournamentDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_JOINING_FEE_DISCOUNT
  const maxTournamentDiscountPercentage = userSubscription?.subscriptionFeatureMaxValue?.TOURNAMENT_JOINING_FEE_DISCOUNT
  const hasSubscriberOnlyTournament = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_SUBSCRIBER_ONLY
  const hasMaxSubscriberOnlyTournament = userSubscription?.subscriptionFeatureMaxValue?.TOURNAMENT_SUBSCRIBER_ONLY

  const CheckSubscription = () => {
    const renderBanner = (content) => (
      <Box className={classes.subscriberBanner}>
        <span>{content}</span>
      </Box>
    )

    const renderJoinOrUpgrade = ({ isUpgrade = false, discount = null, hasExclusive = false }) => {
      const actionText = isUpgrade ? 'UPGRADE NOW' : 'JOIN NOW'
      const prefixText = isUpgrade
        ? 'Upgrade your membership and unlock'
        : 'Become a TMF Plus member today and get'

      return renderBanner(
        <>
          {prefixText}{' '}
          {discount && (
            <>
              up to<strong> {discount}% OFF</strong> entry fees
            </>
          )}
          {hasExclusive && !discount && (
            <>exclusive offers on TMF+ tournament entry fee</>
          )}
          {(discount || hasExclusive) && ' '}and also a chance to win{' '}
          <strong className='tmfPlus'>Vegas Vacation Giveaway Tournament</strong>.
          {isUpgrade ? ' Don’t miss your chance to save and compete! 💥' : ''}{' '}
          <Link to='/subscriptions' className='joinNow'>
            {actionText}
          </Link>
        </>
      )
    }

    // ---- SUBSCRIBERS ----
    if (hasSubscription) {
      const hasDiscount = Boolean(tournamentDiscountPercentage)
      const hasExclusive = Boolean(hasSubscriberOnlyTournament)

      if (hasDiscount && hasExclusive) {
        return renderBanner(
          <>
            🎉 You’re in! As a proud <strong className='tmfPlus'>TMF Plus member</strong>, you’ve unlocked{' '}
            <strong>{tournamentDiscountPercentage}% OFF</strong> your tournament entry fee and TMF+ tournaments
          </>
        )
      }

      if (hasDiscount) {
        return renderBanner(
          <>
            🎉 You’re in! As a proud <strong className='tmfPlus'>TMF Plus member</strong>, you’ve unlocked{' '}
            <strong>{tournamentDiscountPercentage}% OFF</strong> your tournament entry fee
          </>
        )
      }

      if (hasExclusive) {
        return renderBanner(
          <>
            🎉 You’re in! As a proud <strong className='tmfPlus'>TMF Plus member</strong>, you’ve unlocked
            access to TMF+ tournaments
          </>
        )
      }

      // No direct perks → suggest upgrade if max perks exist
      if (maxTournamentDiscountPercentage || hasMaxSubscriberOnlyTournament) {
        return renderJoinOrUpgrade({
          isUpgrade: true,
          discount: maxTournamentDiscountPercentage || null,
          hasExclusive: hasMaxSubscriberOnlyTournament || false
        })
      }

      return null
    }

    // ---- NON-SUBSCRIBERS ----
    if (!hasSubscription) {
      if (maxTournamentDiscountPercentage || hasMaxSubscriberOnlyTournament) {
        return renderJoinOrUpgrade({
          isUpgrade: false,
          discount: maxTournamentDiscountPercentage || null,
          hasExclusive: hasMaxSubscriberOnlyTournament || false
        })
      }

      return null
    }

    return null
  }

  return (
    <Dialog open={open} onClose={hanlePlayWithoutTournament} maxWidth='md' fullWidth className={classes.tournamentModal}>
      <DialogContent>
        <IconButton onClick={hanlePlayWithoutTournament} className='close-btn'>
          <CloseIcon />
        </IconButton>
        <Grid className='modal-header'>
          <Typography variant='h4'>HURRY UP!</Typography>
          <Typography>
            The clock is ticking!
            <br /> Make your move before it’s too late.
          </Typography>
        </Grid>
        {hasActiveSubscription && <CheckSubscription />}
        <Box className='modal-slider-wrap'>
          <Swiper
            spaceBetween={0}
            slidesPerView={1}
            modules={[Navigation]}
            navigation
            breakpoints={{
              0: {
                slidesPerView: 1,
                spaceBetween: 0
              },
              768: {
                slidesPerView: 1,
                spaceBetween: 0
              }
            }}
          >
            {tournamentData?.map((tournament, index) => {
              const isVIP = tournament?.vipTournament // `isVIP` indicates whether it's a VIP tournament
              const isUserAllowedForVIP = tournament?.isUserAllowedInVipTournament && isVIP // Check if the user is allowed for VIP tournaments
              console.log('### tournament', tournament, hasActiveSubscription && tournament?.isSubscriberOnly, isSubscribed, hasSubscriberOnlyTournament)
              return (
                <SwiperSlide key={index}>
                  <Box
                    className={`${classes.tournamentItems} modal-card ${isVIP ? 'vip-tournament' : ''}`}
                    style={{
                      backgroundImage: isVIP
                        ? tournament?.imageUrl
                          ? `url(${tournament?.imageUrl})`
                          : `url(${defaultVipTournamentImage})`
                        : tournament?.imageUrl
                          ? `url(${tournament?.imageUrl})`
                          : `url(${defaultTournamentImage})`
                    }}
                  >
                    {isVIP && !isUserAllowedForVIP && (
                      <Box className='vip-overlay'>
                        <Box className='vip-icon'>
                          <img src={vipLock} alt='lock' />
                        </Box>
                        {tournament?.vipTournamentTitle ? tournament?.vipTournamentTitle : 'Only VIP user can access.'}
                        <Button className='btn tournament-btn-popup' onClick={hanlePlayWithoutTournament}>
                          Not now
                        </Button>
                      </Box>
                    )}
                    <h3>{tournament?.title}</h3>
                    <Grid className='underline' />

                    <h4 className='pool-prize'>POOL PRIZE</h4>
                    {tournament?.entryCoin === 'SC'
                      ? tournament?.winSC > 0 && (
                        <Grid className='sc-badge'>
                          <img src={priceBadge} alt='price-badge' />
                          <span> {formatPriceWithCommas(tournament?.winSC)} SC</span>
                        </Grid>)
                      : tournament?.winGc > 0 && (
                        <Grid className='sc-badge'>
                          <img src={priceBadge} alt='price-badge' />
                          <span> {formatPriceWithCommas(tournament?.winGc)} GC</span>
                        </Grid>
                      )}

                    <h3 className='entry-fee'>
                      {tournament?.isJoined
                        ? 'Tournament Joined'
                        : tournament?.entryAmount === 0
                          ? 'Free Entry'
                          : (
                            <>
                              Entry Fee:{' '}
                              {hasActiveSubscription
                                ? (
                                  isSubscribed && tournamentDiscountPercentage
                                    ? (
                                      <>
                                        <span className='original-price'>
                                          {formatPriceWithCommas(tournament?.entryAmount)} {tournament?.entryCoin}
                                        </span>
                                        <span className='discounted-price'>
                                          {formatPriceWithCommas(
                                            formatDiscountAmount(tournament?.entryAmount, tournamentDiscountPercentage)
                                          )} {tournament?.entryCoin}
                                        </span>
                                      </>)
                                    : (
                                      `${formatPriceWithCommas(tournament?.entryAmount)} ${tournament?.entryCoin}`)
                                )
                                : (
                                  `${formatPriceWithCommas(tournament?.entryAmount)} ${tournament?.entryCoin}`)}
                            </>)}
                    </h3>

                    <Box className='preview'>
                      {hasActiveSubscription && tournament?.isSubscriberOnly
                        ? (
                          isSubscribed && hasMaxSubscriberOnlyTournament
                            ? (
                              hasSubscriberOnlyTournament
                                ? (
                                  <Button
                                    className='btn btn-primary'
                                    onClick={(event) => {
                                      event.stopPropagation()
                                      if (!tournament?.isJoined) {
                                        handleJoinTournament(
                                          tournament?.entryCoin === 'SC'
                                            ? `/cms/tournament-sc-terms`
                                            : `/cms/tournament-gc-terms`,
                                          tournament?.tournamentId, tournament?.entryCoin
                                        )
                                      } else {
                                        handlePlayInTournament(tournament?.tournamentId, tournamentData)
                                      }
                                    }}
                                  >
                                    {tournament?.isJoined ? 'Play Now' : 'Join Now'}
                                  </Button>)
                                : (
                                  <Button
                                    className='tmf-plus-btn btn btn-primary'
                                    onClick={(event) => {
                                      event.stopPropagation()
                                      navigate('/subscriptions')
                                      portalStore.closePortal()
                                    }}
                                  >
                                    <img src={vipLock} alt='lock' className='tmf-lock-icon' />
                                    UPGRADE TMF PLUS
                                  </Button>))
                            : (
                              <Button
                                className='tmf-plus-btn btn btn-primary'
                                onClick={(event) => {
                                  event.stopPropagation()
                                  navigate('/subscriptions')
                                  portalStore.closePortal()
                                }}
                              >
                                <img src={vipLock} alt='lock' className='tmf-lock-icon' />
                                JOIN TMF PLUS
                              </Button>)
                        )
                        : (
                          <Button
                            className='btn btn-primary'
                            onClick={(event) => {
                              event.stopPropagation()
                              if (!tournament?.isJoined) {
                                handleJoinTournament(
                                  tournament?.entryCoin === 'SC'
                                    ? `/cms/tournament-sc-terms`
                                    : `/cms/tournament-gc-terms`,
                                  tournament?.tournamentId, tournament?.entryCoin
                                )
                              } else {
                                handlePlayInTournament(tournament?.tournamentId, tournamentData)
                              }
                            }}
                          >
                            {!tournament?.userInTournament ? 'Join Tournament' : 'Tournament Play'}
                          </Button>)}
                      {/* <Button
                        className='btn btn-primary'
                        onClick={() =>
                          !tournament?.userInTournament
                            ? handleJoinTournament(
                              tournament?.entryCoin === 'SC'
                                ? `/cms/tournament-sc-terms`
                                : `/cms/tournament-gc-terms`,
                              tournament?.tournamentId, tournament?.entryCoin
                            )
                            : handlePlayInTournament(tournament?.tournamentId, tournamentData)
                        }
                      >
                        {!tournament?.userInTournament ? 'Join Tournament' : 'Tournament Play'}
                      </Button> */}
                    </Box>

                    <Button className='btn tournament-btn' onClick={hanlePlayWithoutTournament}>
                      Not now
                    </Button>
                  </Box>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default TournamentPopup
