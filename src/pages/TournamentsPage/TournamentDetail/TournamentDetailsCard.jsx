import React from 'react'
import useStyles from './TournamentDetail.styles'
import { Box, Button, Grid, Typography } from '@mui/material'
import usdIcon from '../../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../../components/ui-kit/icons/opImages/usd-chip.svg'
import { formatPriceWithCommas } from '../../../utils/helpers'
import ParticipentsIcon from '../../../components/ui-kit/icons/webp/participents.webp'
import TournamentStar from '../../../components/ui-kit/icons/webp/tournament-star.webp'
import CountDownTimer from '../../../components/CountDownTimer'
import { useTournamentStore } from '../../../store/useTournamentStore'
import { useNavigate } from 'react-router-dom'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'
import { useUserStore } from '../../../store/useUserSlice'
/* eslint-disable multiline-ternary */

const TournamentDetailsCard = ({ handleJoinTournament }) => {
  const classes = useStyles()
  const navigate = useNavigate()
  const tournamentData = useTournamentStore((state) => state?.tournamentData)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser
  const isSubscribed = userSubscription?.subscriptionDetail !== null
  const isSubscriberOnlyTournament = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_SUBSCRIBER_ONLY

  const RenderJoinButton = () => {
    const entryCoin = tournamentData?.entryCoin
    const termsUrl =
      entryCoin === 'SC'
        ? '/cms/tournament-sc-terms'
        : '/cms/tournament-gc-terms'

    if (!tournamentData?.isJoined) {
      if (hasActiveSubscription && tournamentData?.isSubscriberOnly) {
        if (isSubscribed) {
          if (isSubscriberOnlyTournament) {
            return (
              <Button
                className='btn btn-primary join-btn'
                onClick={() => handleJoinTournament(termsUrl, entryCoin)}
              >
                Join Tournament
              </Button>
            )
          } else {
            return (
              <Button
                className='btn btn-primary join-btn'
                onClick={() => navigate('/subscriptions')}
              >
                Upgrade TMF+
              </Button>
            )
          }
        } else {
          return (
            <Button
              className='btn btn-primary join-btn'
              onClick={() => navigate('/subscriptions')}
            >
              Join TMF+
            </Button>
          )
        }
      } else {
        return (
          <Button
            className='btn btn-primary join-btn'
            onClick={() => handleJoinTournament(termsUrl, entryCoin)}
          >
            Join Tournament
          </Button>
        )
      }
    } else {
      return (
        <Button className={classes.joinTournamentBtn} disabled>
          Joined
        </Button>
      )
    }
  }

  return (
    <Grid container spacing={{ xs: 1, md: 2 }}>
      <Grid item xs={12} md={7}>
        <Box className='border-box'>
          <Box className='tournament-details-wrap'>
            <Typography variant='h2' component='h2'>
              {tournamentData?.title}
            </Typography>
            <Grid container className='tournament-details-grid'>
              <Grid className='tournament-details-card first-card'>
                <Grid className='coin-wrap'>
                  <Button>
                    <Typography variant='span' component='span'>
                      {' '}
                      <img src={usdchipIcon} alt={usdchipIcon} />
                      {formatPriceWithCommas(tournamentData?.winGc)}GC
                    </Typography>
                  </Button>
                  <Button>
                    <Typography variant='span' component='span'>
                      {' '}
                      <img src={usdIcon} alt={usdIcon} /> {formatPriceWithCommas(tournamentData?.winSc)}SC
                    </Typography>
                  </Button>
                </Grid>
              </Grid>
              <Grid className='tournament-details-card'>
                <Box className='tournament-price'>
                  <img src={TournamentStar} alt='' />
                  <Grid className='tournament-price-content'>
                    <Typography>
                      {tournamentData?.entryAmount === 0 ? (
                        <Typography variant='h4'>Entry Free</Typography>
                      ) : (
                        <span className='entry-fee-text'>
                          <Typography>Entry Fee</Typography>
                          <Typography variant='h4'>
                            {' '}
                            {formatPriceWithCommas(tournamentData?.entryAmount)} {tournamentData?.entryCoin}
                          </Typography>
                        </span>
                      )}
                    </Typography>
                  </Grid>
                </Box>

                {tournamentData?.playerLimit != null ? (
                  <Box className='tournament-price entry-fee-card'>
                    <img src={ParticipentsIcon} alt='' />
                    <Grid className='tournament-price-content'>
                      <Typography>No. of players</Typography>
                      <Typography variant='h4'>{tournamentData?.playerLimit}</Typography>
                    </Grid>
                  </Box>
                ) : (
                  <></>
                )}
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Grid>

      <Grid item xs={12} md={5}>
        <Box className='border-box'>
          <Box className='tournament-details-wrap tournament-details-card last-card'>
            <Typography className='tournament-start'>
              {new Date(tournamentData?.startDate) > new Date() ? 'Tournament Start In' : null}
            </Typography>
            <Box className={classes.tournamentBox}>
              <Box className={classes.tournamentTimer}>
                {tournamentData?.status === '2' || tournamentData?.status === '3' ? (
                  <Button
                    disabled
                    className='started-btn'
                    style={{
                      color: tournamentData?.status === '2' ? '#6CCA32' : 'red'
                    }}
                  >
                    {tournamentData?.status === '2' ? 'Completed' : 'Cancelled'}
                  </Button>
                ) : new Date(tournamentData?.startDate) > new Date() ? (
                  <CountDownTimer eventDateTime={tournamentData?.startDate} isLocalAsUTC />
                ) : new Date(tournamentData?.endDate) > new Date() ? (
                  <Button disabled className='started-btn'>
                    Started
                  </Button>
                ) : (
                  <Button disabled className='started-btn'>
                    Completed
                  </Button>
                )}
              </Box>
            </Box>

            <RenderJoinButton />
          </Box>
        </Box>
      </Grid>
    </Grid>
  )
}

export default TournamentDetailsCard
