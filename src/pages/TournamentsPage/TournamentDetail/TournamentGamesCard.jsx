import React, { useState } from 'react'
import { Grid, Box, Typography } from '@mui/material'
import useStyles from './TournamentDetail.styles'
import { whitePlay } from '../../../components/ui-kit/icons/svg'
import { CasinoCard } from '../../../components/ui-kit/icons/utils'
import { Swiper, SwiperSlide } from 'swiper/react'
import { FreeMode, Navigation } from 'swiper/modules'
// import { motion } from 'framer-motion'
import { useParams, useNavigate } from 'react-router-dom'
import { getLoginToken } from '../../../utils/storageUtils'
import MobileVerification from '../../MobileVerification'
import { useUserStore } from '../../../store/useUserSlice'
import { useCoinStore, usePortalStore } from '../../../store/store'
import toast from 'react-hot-toast'
import { useGetProfileMutation } from '../../../reactQuery'
import Signup from '../../../components/Modal/Signup'
import { useTournamentStore } from '../../../store/useTournamentStore'
import { PlayerRoutes } from '../../../routes'
import CmsModal from '../../../components/CmsModal/CmsModal'
import tournamentQuery from '../../../reactQuery/tournamentQuery'
import TournamentJoinModal from './TournamentJoinModal'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'
import LazyImage from '../../../utils/lazyImage'
import PragmaticJackpotSCLogo from '../../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../../components/ui-kit/icons/svg/pragmatic-gc.svg'
import { formatPriceWithCommas } from '../../../utils/helpers'
import { useSubscriptionStore } from '../../../store/useSubscriptionStore'

const TournamentGamesCard = ({ tournamentId }) => {
  const classes = useStyles()
  const tournamentData = useTournamentStore((state) => state?.tournamentData)
  const auth = useUserStore((state) => state)
  const [showJoinModal, setShowJoinModal] = useState(false)
  const { tournamentType } = useParams()
  const coinType = useCoinStore((state) => {
    return state?.coinType
  })

  const setCoinType = useCoinStore((state) => {
    return state?.setCoinType
  })
  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()

  const portalStore = usePortalStore((state) => state)

  const { userDetails, setUserDetails } = useUserStore((state) => ({
    userDetails: state?.userDetails,
    setUserDetails: state?.setUserDetails
  }))

  const navigate = useNavigate()

  const { isScTournamentTermsAccepted, isGcTournamentTermsAccepted } = userDetails || {}

  const [gameId, setGameId] = useState(null)
  const [gameToAutoPlay, setGameToAutoPlay] = useState(null)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser
  const isSubscribed = userSubscription?.subscriptionDetail !== null
  const isSubscriberOnlyTournament = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_SUBSCRIBER_ONLY

  const updateCoinType = () => {
    if (tournamentData?.entryCoin !== coinType) {
      setCoinType(tournamentData?.entryCoin)
      toast(`You've successfully switched to ${tournamentData?.entryCoin} coin!`)
    }
  }

  const handlePlayNow = (masterCasinoGameId, tournamentId) => {
    if (!!getLoginToken() || auth?.isAuthenticate) {
      if (coinType === 'SC' && userDetails?.userWallet?.totalScCoin > 0) {
        updateCoinType()
        navigate(`/game-play-tournament/${masterCasinoGameId}/${tournamentId}`)
      } else if (coinType === 'GC' && userDetails?.userWallet?.gcCoin > 0) {
        updateCoinType()
        navigate(`/game-play-tournament/${masterCasinoGameId}/${tournamentId}`)
      } else {
        toast.error('Please make a purchase!')
        navigate(PlayerRoutes?.Store)
      }
    } else {
      portalStore.openPortal(() => <Signup />, 'signupModal')
    }
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => <MobileVerification calledFor='gamePlay' handlePlayNow={() => handlePlayNow(gameId, tournamentId)} />,
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const handlePlayNowClick = (masterCasinoGameId, tournamentId) => {
    if (coinType === 'SC' && !userDetails?.phoneVerified && (!!getLoginToken() || auth?.isAuthenticate)) {
      setGameId(masterCasinoGameId)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(masterCasinoGameId, tournamentId)
    }
  }

  const handleJoinTournament = (pathname, entryCoin, tournamentId, tournamentData) => {
    if (!!getLoginToken() || auth?.isAuthenticate) {
      if (!userDetails?.phoneVerified) {
        getProfileMutation.mutate()
        setShowJoinModal(false)
      } else {
        handleJoinTournamentFlow()
      }
    } else {
      portalStore.openPortal(() => <Signup />, 'signupModal')
    }

    function handleJoinTournamentFlow() {
      const shouldOpenModal =
        (entryCoin === 'SC' && !isScTournamentTermsAccepted) || (entryCoin === 'GC' && !isGcTournamentTermsAccepted)

      if (shouldOpenModal) {
        portalStore.openPortal(
          () => (
            <CmsModal path={pathname} handleConfirm={() => handleConfirm('submit', tournamentId, tournamentData)} />
          ),
          'cmsModal'
        )
      } else {
        handleConfirm('submit', tournamentId, tournamentData)
      }
    }
  }

  const handleConfirm = (data, tournamentId, tournamentData) => {
    if (data === 'submit' && tournamentId != 0) {
      const payload = {
        tournamentId: tournamentId,
        isTournamentTermsAccepted: true
      }
      mutationJoinTournament.mutate(payload)

      const isScTournamentTermsAccepted = tournamentData?.entryCoin === 'SC'
      const isGcTournamentTermsAccepted = tournamentData?.entryCoin === 'GC'

      setUserDetails({
        ...userDetails,
        isScTournamentTermsAccepted,
        isGcTournamentTermsAccepted
      })
      portalStore.closePortal()
    }
  }

  const successJoinToggler = (data) => {
    toast.success('Tournament Joined successfully.')
    setShowJoinModal(false)

    if (gameToAutoPlay) {
      handlePlayNowClick(gameToAutoPlay?.masterCasinoGameId, tournamentData?.tournamentId)
      setGameToAutoPlay(null)
    }
  }

  const errorJoinToggler = () => { }
  const mutationJoinTournament = tournamentQuery.useJoinTournamentMutation({
    successJoinToggler,
    errorJoinToggler
  })

  return (
    <>
      <Box className={classes.gameSlider}>
        <Typography variant='h4' component='h4'>
          Tournament Games
        </Typography>
        <Grid>
          <Swiper
            spaceBetween={10}
            freeMode={true}
            navigation={{
              nextEl: `#swiper-button-next-${tournamentData?.gameId?.masterCasinoGameId}`,
              prevEl: `#swiper-button-prev-${tournamentData?.gameId?.masterCasinoGameId}`
            }}
            modules={[FreeMode, Navigation]}
            className='mySwiper'
            breakpoints={{
              0: {
                slidesPerView: 3
              },
              768: {
                slidesPerView: 4
              },
              1024: {
                slidesPerView: 8
              }
            }}
          >
            {tournamentData?.gameId?.map((game, idx) => {
              const gameId = String(game?.masterCasinoGameId)

              const isScPragmatic = coinType === 'SC' && pragmaticJackpotSc?.hasOwnProperty(gameId)
              const isGcPragmatic = coinType === 'GC' && pragmaticJackpotGc?.hasOwnProperty(gameId)
              const jackpotValue = isScPragmatic
                ? pragmaticJackpotSc?.[gameId]
                : isGcPragmatic
                  ? pragmaticJackpotGc?.[gameId]
                  : null

              return (
                <SwiperSlide key={`${game?.masterCasinoGameId}_${idx}`}>
                  <div
                    className='custom-col-2'
                    onClick={() => {
                      if (tournamentType === 'onGoing' || tournamentType === 'joined') {
                        if (tournamentData?.isJoined) {
                          handlePlayNowClick(game?.masterCasinoGameId, tournamentData?.tournamentId)
                        } else {
                          if (hasActiveSubscription && tournamentData?.isSubscriberOnly) {
                            if (isSubscribed) {
                              if (isSubscriberOnlyTournament) {
                                setGameToAutoPlay(game)
                                setShowJoinModal(true)
                              } else {
                                toast.error('Please upgrade TMF+ to join this tournament.')
                              }
                            } else {
                              toast.error('Please subscribe to TMF+ to join this tournament.')
                            }
                          } else {
                            setGameToAutoPlay(game)
                            setShowJoinModal(true)
                          }
                        }
                      }
                    }}
                    style={{ cursor: 'pointer' }}
                  >
                    <Grid className='casino-card'>
                      <img src={game?.imageUrl || CasinoCard} className='casinoGame-img' alt='Casino' />
                      <Grid className='overlayPlay'>
                        <Typography variant='h6' sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}>
                          <b>{game?.name}</b>
                        </Typography>

                        {(tournamentType === 'onGoing' || tournamentType === 'joined') && (
                          <Box component='div' sx={{ mt: 1 }}>
                            {tournamentData?.isJoined ? (
                              <>
                                <img
                                  src={whitePlay}
                                  alt='Play'
                                  className={classes.playImg}
                                  style={{ pointerEvents: 'none' }} // Prevent inner click from duplicating
                                />
                                <Typography className='playtext'>
                                  <b>Play Now</b>
                                </Typography>
                              </>
                            ) : (
                              <></>
                            )}
                          </Box>
                        )}
                      </Grid>
                      {jackpotValue !== null && (
                        <div className='prgamatic-jackpot-amount-wrapper'>
                          <LazyImage
                            src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
                            alt='prgamatic-jakcpot-logo'
                          />
                          <Typography
                            style={{
                              color: ` ${coinType === 'SC' ? '#00C80E' : '#FDB72E'}`,
                              fontWeight: '700',
                              fontSize: '10px'
                            }}
                          >
                            {formatPriceWithCommas(jackpotValue)} {coinType}
                          </Typography>
                        </div>
                      )}
                    </Grid>
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </Grid>
      </Box>

      <TournamentJoinModal
        showJoinModal={showJoinModal}
        setShowJoinModal={setShowJoinModal}
        handleJoinTournament={handleJoinTournament}
        tournamentData={tournamentData}
      />
    </>
  )
}

export default TournamentGamesCard
