import { Box, Button, Grid, Link, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import useStyles from '../PromotionsPage/Promotions.styles'
import { useBannerStore } from '../../store/useBannerSlice'
import { useNavigate, useLocation } from 'react-router-dom'
import { useUserStore } from '../../store/useUserSlice'
import { getLoginToken } from '../../utils/storageUtils'
import { GeneralQuery, useRaffleDetailMutation } from '../../reactQuery'
import { usePortalStore } from '../../store/userPortalSlice'
import ViewRaffleTicketDetail from './viewRaffleticketDetail'
import ScrollToTop from '../../components/ScrollToTop'
import Signup from '../../components/Modal/Signup'
import {
  useGetDailyBonusMutation,
  useGetFreeSpinMutation,
  useGetWelcomeBonusMutation
} from '../../reactQuery/bonusQuery'
import WelcomeBonus from '../../components/WelcomeBonus'
import DailyBonus from '../../components/DailyBonus'
import { updateSDKPageVisit } from '../../utils/optimoveHelper'
import LinerProgreeBar from './LInerProgressbar'
import kikLogo from '../../components/ui-kit/icons/svg/kik-logo.svg'
import CmsModal from '../../components/CmsModal/CmsModal'
import GiveAwayCountDownTimer from './GiveAwayCountDownTimer'
import { formatAmount, formatPriceWithCommas } from '../../utils/helpers'
import BannerManagement from '../../components/BannerManagement'
import { convertToPacificShortFormat, formatDateWithOrdinal } from '../../utils/dateFormatter'
import SeoHead from '../../utils/seoHead'

/* eslint-disable multiline-ternary */

const PromotionsPage = () => {
  const navigate = useNavigate()
  const classes = useStyles()
  const auth = useUserStore((state) => state)
  const [raffleDetail, setRaffleDetail] = useState({})
  const portalStore = usePortalStore((state) => state)
  const location = useLocation()
  const currentUrl = window.location.origin + location.pathname + location.search
  const params = new URLSearchParams(window.location.search)
  const [thumbnailData, setThumbnailData] = useState()
  const affiliateCode = params.get('btag') || ''
  const affiliateId = params.get('affid') || ''
  const affiliatePromocode = params.get('promocode') || ''
  const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString() // 15 days from now
  const { promotionBanners } = useBannerStore((state) => state)
  if (affiliateCode || affiliateId || affiliatePromocode) {
    document.cookie = `affiliateCode=${affiliateCode}; expires=${expires}`
    document.cookie = `affiliateId=${affiliateId}; expires=${expires}`
    document.cookie = `affiliatePromocode=${affiliatePromocode}; expires=${expires}`
  }
  if (import.meta.env.VITE_NODE_ENV === 'production') {
    updateSDKPageVisit(currentUrl, 'Promotions')
  }
  const successThumbnailToggler = (res) => {
    if (res?.data?.data) {
      setThumbnailData(res?.data?.data)
    }
  }
  const errorThumbnailToggler = (error) => {
    if (error) {
      console.log(error, 'error')
    }
  }
  const getPromotion =  GeneralQuery.getPromotionThumbnailQuery({ successThumbnailToggler, errorThumbnailToggler })
  useEffect(() => {
    getRaffleDetailMutation.mutate()
    getPromotion.refetch()
  }, [])

  const getRaffleDetailMutation = useRaffleDetailMutation({
    onSuccess: (res) => {
      setRaffleDetail(res?.data?.raffleDetail)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const onLinkClick = (pathname) => {
    portalStore.openPortal(() => <CmsModal path={pathname} />, 'cmsModal')
  }
  const handleViewTickets = (data) => {
    portalStore.openPortal(() => <ViewRaffleTicketDetail ticketData={data} />, 'innerModal')
  }

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data) {
        let resetData = res?.data?.data?.remainingTime
        portalStore.openPortal(() => <DailyBonus dailyBonus={res?.data?.data} resetData={resetData} />, 'bonusStreak')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const mutationGetWelcomeBonus = useGetWelcomeBonusMutation({
    onSuccess: (res) => {
      if (res?.data?.data?.scAmount || res?.data?.data?.gcAmount) {
        portalStore.openPortal(() => <WelcomeBonus welcomeBonusData={res?.data?.data} />, 'bonusModal')
      } else {
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      console.log(error)
    }
  })

  const handelBonus = (key) => {
    if (!!getLoginToken() || auth?.isAuthenticate) {
      if (key?.name === 'welcomeBonus') {
        mutationGetWelcomeBonus.mutate()
      } else if (key?.name === 'dailyBonus') {
        mutationGetDailyBonus.mutate()
      } else {
        navigate(`/${key?.navigateRoute}`)
      }
    } else {
      portalStore.openPortal(() => <Signup />, 'signupModal')()
    }
  }

  return (
    <>
      <SeoHead />
      <ScrollToTop />
      <main className={classes.lobbyRight}>
        <section className='inner-page-container promotion-page'>
          <Grid>
            <BannerManagement bannerData={promotionBanners} />
          </Grid>
          {raffleDetail && raffleDetail?.raffleId ? (
            <section className={classes.giveawaySection}>
              <Grid className='inner-heading'>
                <Typography variant='h4'>{raffleDetail?.title}</Typography>
              </Grid>
              <Grid container spacing={1}>
                <Grid item xs={12} lg={6} className='give-away-order-1'>
                  <GiveAwayCountDownTimer startDateTime={raffleDetail?.startDate} endDateTime={raffleDetail?.endDate} />
                  <Grid className='line-progress-wrap progress-mob'>
                    <Grid className='line-progress-top-content'>
                      <Typography>Next Entry Progress</Typography>
                      <Typography>
                        {formatAmount(raffleDetail?.nextTicket?.requiredAmt)}/{raffleDetail?.nextTicket?.wagerBaseAmt}{' '}
                        {raffleDetail?.wagerBaseAmtType} Played
                      </Typography>
                    </Grid>
                    <LinerProgreeBar rangeValue={raffleDetail?.nextTicket?.progressPercentage} />
                    <Grid className='line-progress-top-content'>
                      <Typography>My Total Entries</Typography>
                      <Typography>{raffleDetail?.userTicket?.length}</Typography>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12} lg={6} className='give-away-order-3'>
                  <Grid className='line-progress-wrap progress-web'>
                    <Grid className='line-progress-top-content'>
                      <Typography>Next Entry Progress</Typography>
                      <Typography>
                        {formatAmount(raffleDetail?.nextTicket?.requiredAmt)}/{raffleDetail?.nextTicket?.wagerBaseAmt}{' '}
                        {raffleDetail?.wagerBaseAmtType} Played
                      </Typography>
                    </Grid>
                    <LinerProgreeBar rangeValue={raffleDetail?.nextTicket?.progressPercentage} />
                    <Grid className='line-progress-top-content'>
                      <Typography>My Total Entries</Typography>
                      <Typography>{raffleDetail?.userTicket?.length}</Typography>
                    </Grid>
                  </Grid>
                  <Grid className='show-ticket-section'>
                    <Grid className='show-ticket-cta'>
                      <Button
                        type='button'
                        className='btn btn-primary'
                        onClick={() => handleViewTickets(raffleDetail?.userTicket)}
                      >
                        Show My Ticket
                      </Button>
                      <Link href='javascript:void(0);' onClick={() => onLinkClick('/cms/giveaway-terms')}>
                        Giveaway rules and terms
                      </Link>
                    </Grid>
                    <Grid className='show-ticket-cta kick-mob-btn'>
                      <Button type='button' className='btn btn-stream'>
                        <a
                          href='https://kick.com/jcyrus'
                          target='_blank'
                          rel='noopener noreferrer'
                          className='kick-link'
                        >
                          Watch Stream <img src={kikLogo} alt='Logo' />
                        </a>
                      </Button>
                      <Typography>
                        Play in {raffleDetail?.wagerBaseAmtType} and earn 1 Entry for each {raffleDetail?.wagerBaseAmt}{' '}
                        {raffleDetail?.wagerBaseAmtType} Played
                      </Typography>
                    </Grid>
                  </Grid>
                  <Grid className='show-ticket-section2'>
                    {raffleDetail?.moreDetails && raffleDetail?.moreDetails.length > 0 ? (
                      <Typography className='tune-in'>
                        TUNE IN! Winners Drawn{' '}
                        {raffleDetail?.moreDetails?.length > 1
                          ? `Daily From ${convertToPacificShortFormat(raffleDetail?.moreDetails[0]?.date)}`
                          : ` On ${convertToPacificShortFormat(raffleDetail?.moreDetails[0]?.date)}`}{' '}
                        PST on{' '}
                        <a
                          href='https://kick.com/jcyrus'
                          target='_blank'
                          rel='noopener noreferrer'
                          className='kick-link'
                          style={{ color: '#00A30B' }}
                        >
                          kick.com/jcyrus{' '}
                        </a>
                      </Typography>
                    ) : (
                      <></>
                    )}
                  </Grid>
                </Grid>
                <Grid item xs={12} lg={12} className='give-away-order-2'>
                  <Grid className='prize-pool-section'>
                    <Grid className='prize-pool-grid'>
                      <Grid className='prize-pool-card'>
                        <Typography variant='h4'>
                          <span className='gold-coin-text'>Total Prize Pool:</span>{' '}
                          <sppan className='shweep-coin-text'>
                            {raffleDetail?.coinType === 'SC'
                              ? formatPriceWithCommas(raffleDetail?.prizeAmountSc) + ' SC'
                              : formatPriceWithCommas(raffleDetail?.prizeAmountGc) + ' GC'}
                          </sppan>
                        </Typography>
                      </Grid>
                      <Grid className='prize-pool-card'>
                        {raffleDetail?.moreDetails?.length >= 1 ? (
                          raffleDetail?.moreDetails.map((prizeDetails) => (
                            <Typography>
                              {formatDateWithOrdinal(prizeDetails?.date)}:{' '}
                              <span className='gold-coin-text'>{prizeDetails?.noOfWinners}</span> winners drawn (
                              <span className='shweep-coin-text'>
                                {formatPriceWithCommas(prizeDetails?.eachAmount)} SC
                              </span>{' '}
                              each )
                            </Typography>
                          ))
                        ) : (
                          <></>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </section>
          ) : (
            <></>
          )}
          {/* old promotion timer and tickets  */}
          <Grid
            className={[classes.wrapper, 'promotions-wrap'].join(' ')}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '2rem'
            }}
          >
            <Grid container spacing={{ xs: 1, md: 1, lg: 1.2 }} style={{ display: 'flex' }}>
              {thumbnailData?.length > 0 &&
                thumbnailData?.map((info, index) => {
                  const isClickable =
                    info?.navigateRoute ||
                    info?.name === 'dailyBonus' ||
                    (info?.name === 'welcomeBonus' && !auth?.userDetails?.isWelcomeBonusClaimed)

                  return (
                    <Grid item xs={12} md={6} key={index}>
                      <Button disabled={!isClickable}>
                        <img
                          src={info?.promotionThumbnailImages}
                          alt='thumbnail'
                          style={{
                            width: '100%',
                            cursor: isClickable ? 'pointer' : 'not-allowed'
                          }}
                          onClick={() => {
                            if (isClickable) {
                              handelBonus(info)
                            }
                          }}
                        />
                      </Button>
                    </Grid>
                  )
                })}
            </Grid>
          </Grid>
        </section>
      </main>
    </>
  )
}

export default PromotionsPage
