import { makeStyles } from '@mui/styles'

import { PackageBanner } from '../../components/ui-kit/icons/banner'
import { InnerBanner, LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    minHeight: 'auto',

    '& .package-page': {
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      '& .subscribe-container': {
        opacity: 1,
        borderRadius: '25px',
        borderWidth: '1px',
        border: '1px solid #FDB72E',
        boxShadow: '0px 4px 4px 0px #FDB72E4D',
        display: 'flex',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        gap: '0.5rem',
        marginBottom: theme.spacing(1.5),
        padding: theme.spacing(0.5, 3),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.5, 1),
          borderRadius: '16px',
          flexDirection: 'column'
        },
        '& .tmf-plus-exclusive': {
          display: 'flex',
          flexDirection: 'column'
        },
        '& .non-subscribe-offers': {
          paddingRight: '0.5rem !important',
          fontWeight: 700,
          fontSize: '30px',
          textShadow: '0 2px 5px #FFA538',
          background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          whiteSpace: 'nowrap',
          [theme.breakpoints.down(1300)]: {
            fontSize: '20px',
            whiteSpace: 'inherit',
            textAlign: 'center'
          },
          [theme.breakpoints.down('md')]: {
            fontSize: '16px'
          }
        },
        '& .tmf-gif': {
          width: '5rem',
          filter: 'drop-shadow(0 0 20px #af0eff)',
          [theme.breakpoints.down('sm')]: {
            width: '4rem'
          }
        }
      },
      '& .non-subscribe-container': {
        opacity: 1,
        borderRadius: '25px',
        borderWidth: '1px',
        border: '1px solid #FDB72E',
        boxShadow: '0px 4px 4px 0px #FDB72E4D',
        display: 'flex',
        justifyContent: 'space-around',
        alignItems: 'center',
        gap: '0.5rem',
        marginBottom: theme.spacing(1.5),
        padding: theme.spacing(0.5, 3),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.5, 1),
          borderRadius: '16px',
          flexDirection: 'column'
        },
        '& .tmf-plus-exclusive': {
          display: 'flex',
          flexDirection: 'column',
          '& .tmf-text': {
            display: 'flex',
            [theme.breakpoints.down('md')]: {
              alignItems: 'center',
              '& img': {
                width: '80px'
              }
            }
          }
        },
        '& .left-section': {
          display: 'flex',
          gap: '0.5rem',
          [theme.breakpoints.down('md')]: {
            '& > img:first-of-type': {
              width: '30px !important',
              height: '30px !important'
            }
          }
        },

        '& .non-subscribe-offers': {
          paddingInline: '0.5rem',
          fontWeight: 700,
          fontSize: '30px',
          textShadow: '0 2px 5px #FFA538',
          background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          whiteSpace: 'nowrap',
          [theme.breakpoints.down(1300)]: {
            fontSize: '20px'
          },
          [theme.breakpoints.down('md')]: {
            fontSize: '16px'
          }
        },
        '& .non-subscribe-exclusive': {
          paddingInline: '0.5rem',
          fontWeight: 500
        },
        '& .join-now': {
          borderRadius: '16.9px',
          border: '1.01px solid #FDB72E',
          fontWeight: 700,
          padding: theme.spacing(0.5, 2),
          width: '100%',
          maxWidth: '180px',
          whiteSpace: 'nowrap',
          [theme.breakpoints.down('sm')]: {
            padding: theme.spacing(0.25, 2)
          }
        },
        '& .tmf-gif': {
          width: '5rem',
          filter: 'drop-shadow(0 0 20px #af0eff)',
          [theme.breakpoints.down('sm')]: {
            width: '4rem'
          }
        }
      },
      '& .free-tag': {
        width: '48px !important',
        [theme.breakpoints.down('sm')]: {
          width: '32px !important'
        }
      },
      '& .member-exclusive': {
        width: '269',
        height: '31',
        opacity: 1,
        borderTopLeftRadius: '29px',
        borderBottomRightRadius: '10px'
      },
      '& .btn-primary': {
        color: theme.colors.textBlack,
        [theme.breakpoints.down('md')]: {
          maxWidth: theme.spacing(12.25),
          marginLeft: 'auto'
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },

      '& .special-offer-section': {
        margin: theme.spacing(4.3125, 0),
        [theme.breakpoints.down('lg')]: {
          margin: theme.spacing(2, 0, 1)
        },

        '& .offer-graphics': {
          width: '14rem',
          [theme.breakpoints.down('sm')]: {
            width: '70%'
          }
        },
        '& .package-details': {
          background: theme.colors.packageDetailsBg,
          border: `1px solid ${theme.colors.packageInnerCard}`,
          padding: theme.spacing(1.25),
          borderRadius: theme.spacing(1.875),
          display: 'flex',
          flexDirection: 'column',
          minHeight: theme.spacing(10.75),
          alignItems: 'center',
          [theme.breakpoints.down('md')]: {
            borderRadius: theme.spacing(0.9375),
            padding: theme.spacing(1, 0.313),
            minHeight: 'auto'
          },
          '& .package-content, & .package-price': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(0.625),
            fontWeight: '500',
            [theme.breakpoints.down('sm')]: {
              gap: theme.spacing(0.3)
            },
            '& img': {
              width: '25px'
            }
          },
          '& .package-price': {
            fontSize: theme.spacing(1.5625),
            [theme.breakpoints.down('md')]: {
              fontSize: theme.spacing(0.875)
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: theme.spacing(0.75)
            },
            '& img': {
              width: '25px'
            }
          },
          '& .MuiButtonBase-root': {
            marginTop: theme.spacing(1.6875),
            fontSize: theme.spacing(1.25),
            minWidth: theme.spacing(16.8125),
            minHeight: theme.spacing(0.125),
            fontWeight: '600',
            [theme.breakpoints.down('md')]: {
              minWidth: theme.spacing(8.4375),
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              margin: '1rem auto 0.625rem'
            }
          }
        },
        '& .MuiGrid-container': {
          alignItems: 'center'
        },
        '& .badge-grid': {
          position: 'absolute',
         
          top: theme.spacing(-1.875),
          right: theme.spacing(0.625),
          [theme.breakpoints.down(1199)]: {
            top: theme.spacing(-2.5)
          },
          [theme.breakpoints.down(983.99)]: {
            top: theme.spacing(-2.5),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(899.99)]: {
            top: theme.spacing(-2.3125),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(600)]: {
            top: theme.spacing(-1.875)
          },
          '& .offer-badge': {
            position: 'relative',
            top: '2px',
            zIndex: 2,
            [theme.breakpoints.down('lg')]: {
              top: '4px'
            },
            [theme.breakpoints.down(1400)]: {
              top: '7px'
            },
            [theme.breakpoints.down('md')]: {
              top: '6px'
            },
            // [theme.breakpoints.down('sm')]: {
            //     top:'2px',
            // },
            '& .sp-offer-badge-content': {
              position: 'absolute',
              top: '39%',
              left: '50%',
              transform: 'translate(-50%,-50%)',
              marginInline: 'auto',
              width: 'fit-content',
              '& h5': {
                fontWeight: 'bold',
                fontSize: theme.spacing(2),
                lineHeight: theme.spacing(2),

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.875),
                  lineHeight: theme.spacing(0.875)
                }
              },
              '& p': {
                fontWeight: 'bold',
                fontSize: theme.spacing(1.25),
                textAlign: 'center',

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75),
                  lineHeight: theme.spacing(0.8)
                }
              }
            },
            [theme.breakpoints.down('sm')]: {
              '& .sp-offer-badge-content': {
                top: '3px',
                left: '50%',
                position: 'absolute',
                marginInline: 'auto',
                width: 'fit-content'
              }
            },
            [theme.breakpoints.down('sm')]: {
              textAlign: 'right',
              top: '2px'
            },

            '& img': {
              [theme.breakpoints.down(1400)]: {
                width: theme.spacing(10)
              },
              [theme.breakpoints.down('md')]: {
                width: theme.spacing(8)
              },
              [theme.breakpoints.down('sm')]: {
                width: theme.spacing(6.25)
              }
            }
          }
        },

        '& .package-details-grid': {
          [theme.breakpoints.down('md')]: {
            order: 3
          }
        }
      },
      '& .subscribe-only-package': {
        background: 'linear-gradient(180deg, #FDD98B 4.81%, #EAB647 38.46%, #846728 100%)',
        borderBottomLeftRadius: '10px',
        borderTopRightRadius: '10px',
        opacity: 1,
        padding: theme.spacing(0, 0.75),
        color: theme.colors.textBlack,
        width: 'max-content',
        position: 'absolute',
        right: '0',
        top: '0',
        '& p': {
          fontWeight: '600',
          fontSize: '14px',
          lineHeight: '1.2'
        },
        [theme.breakpoints.down('lg')]: {
          borderTopLeftRadius: theme.spacing(0.5625)
        }
      },
      '& .package-card': {
        background: theme.colors.coinBundle,
        border: `1px solid ${theme.colors.packageInnerCard}`,
        padding: theme.spacing(0, 1.25, 0.2),
        borderRadius: theme.spacing(2),
        minHeight: theme.spacing(13.4375),
        position: 'relative',
        [theme.breakpoints.down('lg')]: {
          padding: theme.spacing(1)
        },
        [theme.breakpoints.down('md')]: {
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.9375)
        },
        '&.tmf-plus': {
          paddingTop: '1.5rem'
        }
      },
      '& .tmf-plus-user-card': {
        border: '1px solid #FDB72E !important',
        // boxShadow: '0px 4px 4px 0px #FFA53866 !important',
        '& .package-details': {
          opacity: 0.5
        }
      },
      '& .purchase-section': {
        marginTop: '1rem',
        '& .purchase-section-content': {
          background: theme.colors.coinBundle,
          border: `1px solid ${theme.colors.packageInnerCard}`,
          padding: theme.spacing(2.5, 2.0625),
          borderRadius: theme.spacing(2.5),
          [theme.breakpoints.down('lg')]: {
            borderRadius: theme.spacing(1)
          },
          [theme.breakpoints.down('md')]: {
            padding: theme.spacing(0.9375)
          },
          '& .package-card': {
            background: '#20242C',
            border: '1px solid #4D535F',
            minHeight: theme.spacing(6.1875),
            marginBottom: theme.spacing(2.125),
            borderRadius: theme.spacing(2),

            [theme.breakpoints.down('lg')]: {
              marginBottom: theme.spacing(1),
              borderRadius: theme.spacing(0.5625)
            },
            '&:last-child': {
              marginBottom: '0'
            },
            '& .MuiGrid-container': {
              alignItems: 'center',
              '& .bonus-badge': {
                position: 'relative',
                left: theme.spacing(-2.875),
                [theme.breakpoints.down(1500)]: {
                  left: theme.spacing(-2.625)
                  // width: theme.spacing(5.625),
                },
                [theme.breakpoints.down(1400)]: {
                  width: theme.spacing(5.625)
                },
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(7),
                  left: theme.spacing(-2.75)
                },
                [theme.breakpoints.down('md')]: {
                  left: theme.spacing(-2.625)
                },
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(4),
                  left: theme.spacing(-1.9375)
                }
              },
              '& .bonus-graphics': {
                // height: theme.spacing(6.25),
                width: theme.spacing(8.25),
                [theme.breakpoints.down(1300)]: {
                  width: theme.spacing(6.5)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.625)
                },
                [theme.breakpoints.down(360)]: {
                  width: theme.spacing(3.5)
                }
              }
            },
            '& .package-details': {
              background: theme.colors.Pastel,
              padding: theme.spacing(0.625),
              minHeight: theme.spacing(3.75),
              borderRadius: theme.spacing(1.875),
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              [theme.breakpoints.down('md')]: {
                borderRadius: theme.spacing(1.875),
                minHeight: theme.spacing(2.0625),
                marginTop: theme.spacing(0.313)
              },
              '& .package-content, & .package-price': {
                display: 'flex',
                alignItems: 'center',
                gap: theme.spacing(0.5)
              },
              '& .package-price': {
                fontSize: theme.spacing(1.125),
                fontWeight: '600',
                [theme.breakpoints.down(1450)]: {
                  fontSize: theme.spacing(0.875)
                },
                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75)
                },
                '& img': {
                  width: '25px',
                  [theme.breakpoints.down('sm')]: {
                    width: theme.spacing(1)
                  },
                  '&.free-tag': {
                    [theme.breakpoints.down('md')]: {
                      width: '40px !important'
                    },
                    [theme.breakpoints.down('sm')]: {
                      width: '32px !important'
                    }
                  }
                },
                '&.extra-prize': {
                  [theme.breakpoints.down('sm')]: {
                    display: 'none'
                  }
                },
                '& .scratch-price': {
                  background: 'linear-gradient(180deg, #F7C35C 56.66%, #DD970F 83.52%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: '700',
                  textShadow: '0px 4px 4px 0px #00000066'
                }
              }
            },
            '& .mob-extra-prize': {
              display: 'none',
              [theme.breakpoints.down('sm')]: {
                display: 'flex',
                marginTop: '0.5rem',
                alignItems: 'center',
                justifyContent: 'center',
                gap: theme.spacing(0.5)
              },
              '& img': {
                width: theme.spacing(1)
              },
              '& .scratch-price': {
                background: 'linear-gradient(180deg, #F7C35C 56.66%, #DD970F 83.52%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '700',
                textShadow: '0px 4px 4px 0px #00000066'
              }
            },
            // '& .extra-prize': {
            //   height: '100px'
            // },
            '& .order-1': {
              [theme.breakpoints.down(1199)]: {
                justifyContent: 'flex-start',
                display: 'flex',
                alignItems: 'center'
              }
            },
            '& .order-2': {
              [theme.breakpoints.down(1199)]: {
                order: 2
              }
            },
            '& .order-3': {
              [theme.breakpoints.down(1199)]: {
                order: 3
              }
            },
            '& .package-img-wrap': {
              display: 'flex',
              alignItems: 'center',
              '& .package-img': {
                position: 'relative',
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(3)
                },
                '& .offer-badge-content-text': {
                  top: '49%',
                  left: '14%',
                  position: 'absolute',
                  textAlign: 'center',
                  transform: 'translate(-50%, -50%)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  [theme.breakpoints.down(1500)]: {
                    top: '48%',
                    left: '14%'
                  },

                  // [theme.breakpoints.down(1400)]: {

                  //     left: "0%",
                  // },
                  [theme.breakpoints.down(1199)]: {
                    left: '8%'
                  },

                  [theme.breakpoints.down(767)]: {
                    top: '44%',
                    left: '7%'
                  },

                  [theme.breakpoints.down(460)]: {
                    left: '0%'
                  },
                  '& h5': {
                    fontWeight: 'bold',
                    fontSize: theme.spacing(1.25),
                    lineHeight: theme.spacing(1.25),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.875),
                      lineHeight: theme.spacing(0.875)
                    }
                  },
                  '& p': {
                    fontWeight: 'bold',
                    fontSize: theme.spacing(0.875),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.75),
                      lineHeight: theme.spacing(0.625)
                    }
                  }
                }
              }
            },

            '&:first-child': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #1362BE99)'
              }
            },
            '&:nth-child(2)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #F8D54ECC)'
              }
            },
            '&:nth-child(3)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #3BBE13CC)'
              }
            },
            '&:nth-child(4)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(5)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(6)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #00A30BCC)'
              }
            },
            '&:nth-child(7)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C4009DCC)'
              }
            },
            '&:nth-child(8)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C44800CC)'
              }
            },
            '& .offer-badge-tag': {
              position: 'absolute',
              left: theme.spacing(-1.3125),
              top: 0,
              zIndex: 1,
              [theme.breakpoints.down('lg')]: {
                left: theme.spacing(-2)
              },
              [theme.breakpoints.down('md')]: {
                left: theme.spacing(-1.825)
              },
              [theme.breakpoints.down('sm')]: {
                left: theme.spacing(-1.875)
              },

              '& img': {
                width: theme.spacing(9.375),
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(6.375)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.375)
                }
              },
              '& .offer-badge-content': {
                position: 'absolute',
                zIndex: 2,
                transform: 'translate(-50%, -50%)',
                left: '50%',
                top: '50%',
                [theme.breakpoints.down('lg')]: {
                  top: '47%'
                },
                '& h5, & p': {
                  fontWeight: theme.typography.fontWeightExtraBold,
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(0.875),
                    textAlign: 'center'
                  },
                  [theme.breakpoints.down('md')]: {
                    fontSize: theme.spacing(0.75),
                    lineHeight: theme.spacing(1)
                  }
                }
              }
            }
          }
        },
        '& .package-btn-wrap': {
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          '& .MuiButtonBase-root': {
            fontSize: theme.spacing(1.25),
            padding: theme.spacing(0.4, 1.5),
            fontWeight: '600',
            minWidth: theme.spacing(14.5625),
            minHeight: theme.spacing(3.25),
            [theme.breakpoints.down(1499)]: {
              minWidth: theme.spacing(11.5625),
              width: '100%'
            },
            [theme.breakpoints.down(1080)]: {
              minWidth: 'auto',
              fontSize: theme.spacing(1),
              minHeight: theme.spacing(2.5)
            },

            [theme.breakpoints.down('md')]: {
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              padding: theme.spacing(0.4, 0.625)
            }
          }
        }
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(1.125)
        },
        '& h4': {
          fontSize: theme.spacing(1.875),
          fontWeight: '600',
          lineHeight: theme.spacing(2),

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    },
    '& .explosive-section': {
      textAlign: 'center',
      color: '#fff',
      padding: '0 0 24px',
      [theme.breakpoints.down('md')]: {
        padding: '0 0 2px'
      },
      '& .online-players': {
        color: '#4caf50',
        fontSize: 14,
        fontWeight: 500,
        marginBottom: 8
      },

      '& .main-title': {
        fontSize: 36,
        fontWeight: 800,
        textTransform: 'uppercase',
        color: '#fff',
        marginBottom: 12,
        textShadow: '0 0 12px rgba(255,255,255,0.4)',
        [theme.breakpoints.down('md')]: {
          fontSize: 25
        }
      },

      '& .subtitle': {
        color: '#fdb72e',
        fontSize: 18,
        fontWeight: 700,
        marginBottom: 16
      },

      '& .cta': {
        color: '#00ff66',
        fontWeight: 700,
        fontSize: 16,
        marginBottom: 16,
        textShadow: '0 0 10px rgba(0,255,100,0.4)'
      },

      '& .features-row': {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 24,
        fontSize: 14,
        color: '#9CA3AF',
        marginBottom: 24,
        '& p': {
          fontWeight: 700
        },
        '& .feature-star-rating': {
          display: 'flex',
          alignItems: 'center'
        }
      },

      '& .sub-banner': {
        background: 'linear-gradient(90deg, #0d2c24, #0a1412)',
        border: '1px solid #fdb72e',
        borderRadius: 8,
        padding: '16px',
        marginTop: 20,
        boxShadow: '0 0 12px rgba(253,183,46,0.3)',
        '& .sub-banner-title': {
          fontWeight: 700,
          fontSize: 16,
          color: '#fff',
          marginBottom: 4
        },
        '& .sub-banner-sub': {
          fontSize: 14,
          // color: '#ff6600',
          color: 'rgb(253 183 46 / var(--tw-text-opacity, 1))',
          fontWeight: 600
        }
      }
    },
    '& .pack-card': {
      background: '#111',
      border: '1px solid #616161ff',
      borderRadius: 12,
      padding: '16px 16px 0',
      // maxWidth: 280,
      height: '100%',
      width: '100%',
      color: '#fff',
      position: 'relative',
      textAlign: 'center',
      boxShadow: '0 4px 12px rgba(0,0,0,0.5)',
      transition: '200ms all ease-in-out',
      '&:hover': {
        border: '1px solid #fdb72e',
        transform: 'translateY(-5px)'
      },

      '& .badge': {
        color: '#fff',
        fontSize: 12,
        fontWeight: 700,
        padding: '2px 8px',
        position: 'absolute',
        top: '-1.75rem',
        left: '0',
        borderRadius: 12,
        display: 'inline-block',
        marginBottom: 8,
        textTransform: 'uppercase'
      },

      '& .title': {
        fontWeight: 600,
        fontSize: 16,
        marginBottom: 12,
        [theme.breakpoints.down('md')]: {
          // fontSize: 16,
          marginBottom: 0
        }
      },

      '& .gc-text': {
        fontWeight: 700,
        fontSize: '1rem',
        marginBottom: 12,
        color: ' #fdb72e',
        '& span': {
          height: '1.25rem',
          width: '1.25rem',
          borderRadius: '100%',
          background: theme.colors.YellowishOrange,
          marginRight: '0.5rem',
          fontSize: '1rem',
          color: '#000',
          paddingLeft: '.0938rem'
        },
        [theme.breakpoints.down('md')]: {
          fontSize: '0.875rem'
        }
      },
      '& .box-wrap': {
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: 'column'
      },

      '& .bonus-box': {
        background: '#194418',
        border: '1px solid #00ff4e80',
        padding: '8px',
        borderRadius: 8,
        marginBottom: 12,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        '& .bonus-title': {
          fontWeight: 700,
          color: '#00ff66',
          display: 'flex',
          alignItems: 'center',
          '& img': {
            width: '1rem'
          }
        },
        '& .bonus-sub': {
          fontWeight: 700,
          fontSize: 14,
          color: '#fff'
        },
        '& .extra-bonus': {
          fontSize: '14px',
          fontWeight: '700',
          '& img': {
            width: '20px'
          }
        }
      },

      '& .price': {
        color: '#fdb72e',
        fontWeight: 700,
        fontSize: 20
        // marginBottom: 8
      },

      '& .buy-btn': {
        background: '#fdb72e !important',
        color: '#000 !important',
        fontWeight: 700,
        borderRadius: 20,
        padding: '6px 16px',
        marginBottom: 16
      },

      '& .card-meta': {
        fontSize: 12,
        marginBottom: 12,
        textAlign: 'start',
        [theme.breakpoints.down('md')]: {
          fontSize: 11,
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 8
        },

        '& .purchased': { color: '#4caf50 ', fontWeight: 700, fontSize: '0.75rem' },
        '& .viewing': { color: '#bbb', fontWeight: 700, fontSize: '0.75rem', display: 'flex', alignItems: 'center', }
      },

      '& .card-footer': {
        display: 'grid',
        gap: 4,
        borderTop: '1px solid #cccccca2',
        paddingTop: '0.5rem',

        gridTemplateColumns: 'repeat(2, 1fr)',
        fontSize: 12,
        textAlign: 'left',
        marginTop: 8,
        '& p, & span, & .MuiTypography-root': {
          fontSize: 12
        },
        '& .instant-check': {
          color: '#4ADE80',
          fontWeight: '700'
        },
        '& .rapid-payout': {
          color: '#C084fc',
          fontWeight: '700'
        },
        '& .bit-ssl': {
          color: '#60A5FA',
          fontWeight: 700
        },
        '& .support': {
          color: '#FB923C',
          fontWeight: 700
        }
      },
      '& .buy-wrap': {
        display: 'flex',
        justifyContent: 'space-between',
        maxWidth: '300px',
        margin: '0 auto',
        width: '100%',
        '& button': {
          padding: '4px 12px',
          fontWeight: 700,
          '& span': {
            display: 'flex',
            alignItems: 'center',
            '& svg': {
              width: '1.25rem'
            }
          }
        }
      }
    },
    '& .special-package': {
      border: '2px solid #fdb72e',
      maxWidth: '400px',
      margin: '0 auto',
      '& .card-header': {
        '& p': {
          fontSize: '1rem'
        }
      }
    },

    '& .footer-cta-container': {
      margin: theme.spacing(1, 0),
      border: '1px solid #fdb72e',
      background: 'linear-gradient(to right, #132B27, #1B181E)',
      borderRadius: theme.spacing(2.5),
      [theme.breakpoints.down('md')]: {
        margin: theme.spacing(0),
        marginBottom: '1rem',
        borderRadius: theme.spacing(1.5)
      },

      '& .footer-cta-content': {
        textAlign: 'center',
        padding: theme.spacing(1.5),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(1)
        },
        '& span': {
          color:'#fdb72e',
          fontWeight:'bold'
        },

        '& h5': {
          fontWeight: 'bolder'
          // padding: theme.spacing(1)
        },
        '& .security-text': {
          fontSize: 14,
          fontWeight: 500,
          color: '#00c853',
          marginBottom: 16
        },

        '& .logos-row': {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 12,
          marginBottom: '1rem'
        },

        '& .logo-card': {
          background: '#f5f5f5',
          borderRadius: 8,
          padding: '8px 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: 80,
          minHeight: 40,
          boxShadow: '0 2px 6px rgba(0,0,0,0.15)'
        },

        '& .text-logo': {
          fontWeight: 600,
          fontSize: 14,
          color: '#000'
        }
      },
      '& .footer-cta': {
        display: 'flex !important',
        gap: theme.spacing(1),
        padding: theme.spacing(1.5, 0),
        justifyContent: 'center',

        [theme.breakpoints.down('md')]: {
          flexDirection: 'column'
        },

        '& h6': {
          fontWeight: 'bold'
        },
        '& p': {
          fontWeight: '400'
        },
        '& .icon-container': {
          background: '#fdb72e',
          borderRadius: '50%',
          width: '3rem',
          padding: theme.spacing(0.5),
          height: '3rem',

          margin: 'auto',
          '& svg': {
            fill: '#000',
            width: '100%',
            height: '100%'
          }
        }
      },
      '& .feedback-slider-container': {
        padding: theme.spacing(2, 2, 3, 2),
        background: '#1B181E',
        border: '1px solid #4D535F',
        textAlign: 'left',
        borderRadius: theme.spacing(1),
        '& .swiper-pagination-bullet-active': {
          background: '#fdb72e !important' /* same yellow */,
          opacity: 1 /* full visibility */
          
        },
        '& .swiper-pagination-bullet': {
          background: 'grey',
          height: '1rem',
          width: '1rem'
        },
        '& .feedback-box-container': {
          // padding: theme.spacing(2, 2, 3, 2),
          // background: '#1B181E',
          // border: '1px solid #4D535F',
          // textAlign: 'left',
          // borderRadius: theme.spacing(1),
          '& .feedback-box': {
            display: 'flex',
            gap: '1.5rem',
            justifyContent: 'center',
            '& .feedback-content': {
              textAlign: 'left',
              '& p': {
                fontWeight: 'bold'
              },
              '& .packageName': {
                color: theme.colors.YellowishOrange
              }
            },
            '& img': {
              width: '8rem',
              height: '8rem',
              [theme.breakpoints.down('md')]: {
                width: '4rem',
                height: '4rem'
              }
            }
          }
        }
      },
      '& .why-choose-tmf': {
        display: 'flex',
        gap: theme.spacing(1),
        [theme.breakpoints.down('md')]: {
          flexDirection: 'column'
        },

        '& .feedback-box': {
          padding: theme.spacing(1),
          background: '#1B181E',
          border: '1px solid #4D535F',
          borderRadius: theme.spacing(1),

          '& .rating-name': {
            display: 'flex',
            '& span': {
              color: '#fdb72e',
              fontWeight: 500
            }
          }
        }
      },
      '& .winning-btn': {
        margin: '1rem',
        fontSize: '1rem',
        borderRadius: '0.5rem',

        [theme.breakpoints.down('md')]: {
          maxWidth: '100%',
          margin: '0',
          marginTop: '0.5rem'
          // fontSize: '1rem'
        }
      },
      '& .join-winners': {
        fontWeight: '500'
      },
      '& .swiper-button-prev:after,& .swiper-button-next:after': {
        color: '#fdb72e',
        fontSize: '1.5rem'
      }
    },
    '& .original-price': {
      position: 'relative',
      display: 'inline-block',
      color: '#ccc',
      fontWeight: 'bold',
      marginRight: '0.25rem',
      fontSize: '14px',
      textDecorationLine: 'line-through',
      textDecorationColor: 'red',
      textDecorationThickness: '3px'
    },
    '& .discounted-price': {
      color: 'green'
    }
  },
  InnerBanner: {
    ...InnerBanner(theme),
    background: `url(${PackageBanner})`
  },
  lobbySearchWrap: {
    marginBottom: theme.spacing(3.5),
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: 'none'
      // marginTop: theme.spacing(6.25),
    },
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(0.75),
      left: theme.spacing(1.25)
    },
    '& .MuiTextField-root': {
      width: '100%',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(2.18),
        border: `2px solid ${theme.colors.Pastel}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        minHeight: theme.spacing(2),
        '&::placeholder': {
          color: `${theme.colors.placeHolderText} !important`,
          fontSize: `${theme.spacing(1)} !important`,
          fontWeight: '500 !important'
        },

        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(2.18)
        }
      }
    }
  }
}))
