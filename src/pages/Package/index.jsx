import React, { useEffect } from 'react'
import useStyles from './style'
import { Box, Button, CircularProgress, Grid, Rating, Typography } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import outlinedEye from '../../components/ui-kit/icons/svg/outlinedEye.svg'
import StarIcon from '../../components/ui-kit/icons/svg/starIcon.svg'
import usePackages from '../Store/hooks/usePackages'
import { formatPriceWithCommas, formatValueWithB, formatAmount, formatDiscountAmount } from '../../utils/helpers'
import StepperForm from '../../components/StepperForm'
import { useBannerStore } from '../../store/useBannerSlice'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import PaymentStatus from '../../components/PaymentStatus'
import usePaysafePayment from '../Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../../components/Loader/PaymentLoader'
import { usePaymentProcessStore, useUserStore } from '../../store/store'
import scratchCardIcon from '../../components/ui-kit/icons/png/scratch-card-icon.png'
import { GiftIcon, JoinTMFPlus, MasterImg, TMFPlus, VisaImg } from '../../components/ui-kit/icons/svg'
import LazyImage from '../../utils/lazyImage'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { PlayerRoutes } from '../../routes'
import StarBorderIcon from '@mui/icons-material/StarBorder'

import packageGif from '../../components/ui-kit/icons/gif/package.gif'
import LockRoundedIcon from '@mui/icons-material/LockRounded'
import BoltOutlinedIcon from '@mui/icons-material/BoltOutlined'
import WorkspacePremiumOutlinedIcon from '@mui/icons-material/WorkspacePremiumOutlined'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination, Navigation } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
import useGetDeviceType from '../../utils/useGetDeviceType'

const Package = () => {
  const { status } = useParams()
  const { setPackageData } = usePaysafePayment()
  const portalStore = usePortalStore()
  const navigate = useNavigate()
  const classes = useStyles()

  const location = useLocation()
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const cancelDepositOpen = usePaymentProcessStore((state) => state.cancelDepositOpen)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const { isMobile } = useGetDeviceType()
  // TMF+ SUBSCRIPTION
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser
  const hasSubscription = userSubscription?.subscriptionDetail !== null && hasActiveSubscription
  const packageDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.PACKAGE_EXCLUSIVE_DISCOUNT
  const maxPackageDiscountPercentage = userSubscription?.subscriptionFeatureMaxValue?.PACKAGE_EXCLUSIVE_DISCOUNT
  const hasSubscriberOnlyPackage = userSubscription?.subscriptionFeatureDetail?.PACKAGE_SUBSCRIBER_ONLY
  const hasMaxSubscriberOnlyPackage = userSubscription?.subscriptionFeatureMaxValue?.PACKAGE_SUBSCRIBER_ONLY

  const { packageData, isloading, refetch } = usePackages()
  const packageRow = packageData?.rows?.[0]

  const handleBuyNow = (item) => {
    if (
      (!hasSubscription || hasSubscription) &&
      item?.isSubscriberOnly &&
      maxPackageDiscountPercentage &&
      !packageDiscountPercentage
    ) {
      navigate(PlayerRoutes.Subscriptions)
      return
    }
    ; (function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageData(item)
    portalStore.openPortal(
      () => (
        <>
          <Box className='stepper-outer-box'>
            <StepperForm stepperCalledFor={'purchase'} packageDetails={item} />
          </Box>
        </>
      ),
      'StepperModal'
    )
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')

    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: params.get('status'),
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      handlePaymentSuccess(data)
    }
  }, [location])

  const handlePaymentSuccess = (data) => {
    refetch()
    portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'loginModal')
  }

  const CheckSubscription = () => {
    const renderSubscribed = (message) => (
      <Grid>
        <Box className='subscribe-container'>
          <img src={packageGif} className='tmf-gif' alt='gif' />
          <Box className='tmf-plus-exclusive'>
            <Box className='non-subscribe-offers'>{message}</Box>
            <Typography className='non-subscribe-offers'>KEEP SPINNING — more surprises are on the way!</Typography>
          </Box>
        </Box>
      </Grid>
    )

    const renderNonSubscribed = (buttonText, buttonAction, showUpgrade = false) => (
      <Grid>
        <Box className='non-subscribe-container'>
          <img src={packageGif} className='tmf-gif' alt='gif' />
          <Box className='left-section'>
            <Box className='tmf-plus-exclusive'>
              <Box className='tmf-text'>
                {showUpgrade && <span className='non-subscribe-offers'>UPGRADE</span>}
                <LazyImage
                  src={showUpgrade ? TMFPlus : JoinTMFPlus}
                  alt='TMF+'
                  sizes='100px'
                  priority
                  style={{ cursor: 'pointer', height: '2rem' }}
                />
                <span className='non-subscribe-offers'> TO GET EXCLUSIVE OFFERS.</span>
              </Box>
              <Typography className='non-subscribe-exclusive'>
                Exclusive bonuses and surprises await. One spin could change everything!
              </Typography>
            </Box>
          </Box>

          <Button type='button' className='btn btn-secondary join-now' onClick={() => navigate(buttonAction)}>
            {buttonText}
          </Button>
        </Box>
      </Grid>
    )

    // --- SUBSCRIBER CASES ---
    if (hasSubscription) {
      const hasDiscount = Boolean(packageDiscountPercentage)
      const hasExclusive = Boolean(hasSubscriberOnlyPackage)

      // Subscriber with discount
      if (hasDiscount && hasExclusive) {
        return renderSubscribed(`YOU’RE ENJOYING ${packageDiscountPercentage}% OFF ON ALL TMF+ DEALS!`)
      }

      if (hasDiscount) {
        return renderSubscribed(`YOU’RE ENJOYING ${packageDiscountPercentage}% OFF ON EXCLUSIVE TMF+ DEALS!`)
      }

      if (hasExclusive) {
        return renderSubscribed('YOU’RE ENJOYING ACCESS ON EXCLUSIVE TMF+ DEALS!')
      }

      // No perks → suggest upgrade if max values exist
      if (maxPackageDiscountPercentage || hasMaxSubscriberOnlyPackage) {
        return renderNonSubscribed('UPGRADE NOW', PlayerRoutes.Subscriptions, true)
      }

      return null
    }

    // --- NON-SUBSCRIBER CASES ---
    if (!hasSubscription) {
      if (maxPackageDiscountPercentage || hasMaxSubscriberOnlyPackage) {
        return renderNonSubscribed('JOIN NOW', PlayerRoutes.Subscriptions)
      }

      return null
    }

    return null
  }

  const colors = ['#22C55E', '#3B82F6', '#FDB72E', '#A855F7', 'linear-gradient(to right,#facc15,#f97316)']
  const whyChooseTMFReview = [
    {
      feedback: '"Amazing experience! Won big on my first day and the coins were delivered instant..."',
      name: ' Sarah Johnson, High Roller',
      rating: '5'
    },
    {
      feedback: '"Best social casino I\'ve ever played! The games are incredible and I love getting..."',
      name: 'Mike Rodriguez, VIP Pack',
      rating: 5
    }
  ]

  const testimonialsData = [
    {
      name: 'Sarah Johnson',
      location: 'Austin, TX',
      text: '"Amazing experience! Won big on my first day and the coins were delivered instantly. Customer service is top-notch!"',
      highlight: 'Best Rewards Ever',
      rating: 5,
      packageName: 'High Roller Player',
      image: 'https://randomuser.me/api/portraits/women/65.jpg'
    },
    {
      name: 'Mike Rodriguez',
      location: 'Miami, FL',
      text: '"Best social casino I\'ve ever played! The games are incredible and I love getting free sweepcoins with every purchase."',
      highlight: '1 Year Loyalty',
      rating: 5,
      packageName: 'VIP Pack Player',
      image: 'https://randomuser.me/api/portraits/men/45.jpg'
    },
    {
      name: 'Emma Chen',
      location: 'San Francisco, CA',
      text: '"I\'ve been playing for 6 months and this is hands down the most reliable platform. Fast payouts and amazing bonuses!"',
      highlight: 'Amzing Support',
      rating: 5,
      packageName: 'Gold Rush Player',
      image: 'https://randomuser.me/api/portraits/women/22.jpg'
    },
    {
      name: 'David Thompson',
      location: 'Chicago, IL',
      text: '"Incredible variety of games and the security is unmatched. I feel completely safe making purchases here."',
      highlight: 'Big Wins',
      rating: 4,
      packageName: 'Millionaire Player',
      image: 'https://randomuser.me/api/portraits/men/33.jpg'
    },
    {
      name: 'Jessica Martinez',
      location: 'Phoenix, AZ',
      text: '"The Money Factory changed my gaming experience completely. Professional, secure, and so much fun!"',
      highlight: 'Flexibility',
      rating: 5,
      packageName: 'Player Pack Player',
      image: 'https://randomuser.me/api/portraits/women/12.jpg'
    },
    {
      name: 'Robert Kim',
      location: 'Seattle, WA',
      text: '"Outstanding customer support and lightning-fast transactions. This is the gold standard for social casinos!"',
      highlight: 'Easy & Secure',
      rating: 5,
      packageName: 'The Factory Player',
      image: 'https://randomuser.me/api/portraits/men/28.jpg'
    },
    {
      name: 'Amanda Wilson',
      location: 'Denver, CO',
      text: '"I love the instant delivery and the games are so engaging. Been a loyal customer for over a year now!"',
      highlight: 'Easy & Secure',
      rating: 5,
      packageName: 'High Roller Player',
      image: 'https://randomuser.me/api/portraits/women/28.jpg'
    },
    {
      name: 'Chris Anderson',
      location: 'Las Vegas, NV',
      text: '"As someone from Vegas, I know good gaming when I see it. The Money Factory delivers premium quality every time."',
      highlight: 'Easy & Secure',
      rating: 5,
      packageName: 'VIP Player Pack',
      image: 'https://randomuser.me/api/portraits/men/29.jpg'
    }
  ]

  return (
    <>
      {status && <PaymentStatus status={status} isTrustly />}
      {(isPaymentScreenLoading || cancelDepositOpen) && <PaymentLoader />}
      <Grid className={classes.lobbyRight}>
        <Box className='package-page'>
          {isloading && (
            <div>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
              Loading...
            </div>
          )}
          <Grid className='explosive-section'>
            <Typography className='main-title glow-text'>EXPLOSIVE COIN PACKAGES</Typography>

            {!isMobile && (
              <>
                <Typography className='subtitle text-flash'>⚡ INSANE BONUSES + FREE SWEEPCOINS ⚡</Typography>

                <Typography className='cta glow-text'> 🎰 START YOUR COIN JOURNEY TODAY! 🎰</Typography>

                <Box className='features-row'>
                  <Typography>🛡 Bank Security</Typography>
                  <Typography>⚡ Instant Delivery</Typography>
                  <Typography className='feature-star-rating'>
                    <Typography className='star-rotate'>
                      <StarBorderIcon style={{ color: '#fdb72e', stroke: 'fdb72e', strokeWidth: 1 }} />
                    </Typography>
                    <span style={{ marginLeft: '6px' }}> 4.7/5 Rating</span>
                  </Typography>
                  <Typography>💳 All Payments</Typography>
                </Box>

                {/* Sub Banner */}
                <Box className='sub-banner glow-card'>
                  <Typography className='sub-banner-title text-flash'>
                    <img src={GiftIcon} className='bounce' me={1} />⚡ FREE SWEEPCOINS WITH EVERY PURCHASE! ⚡
                  </Typography>
                  <Typography className='sub-banner-sub'>🔥 Get Gold Coins + FREE Sweepscoins to play!</Typography>
                </Box>
              </>
            )}
          </Grid>

          {hasActiveSubscription && <CheckSubscription />}
          {packageRow?.isSpecialPackage === true && (
            <Grid container>
              <Grid item xs={12} md={4} style={{ margin: '0 auto' }}>
                <Grid
                  className={
                    hasActiveSubscription
                      ? `pack-card tmf-plus glow-card ${hasSubscription &&
                        packageRow?.isSubscriberOnly &&
                        maxPackageDiscountPercentage &&
                        packageDiscountPercentage
                        ? ''
                        : (hasSubscription || !hasSubscription) &&
                          packageRow?.isSubscriberOnly &&
                          maxPackageDiscountPercentage &&
                          !packageDiscountPercentage
                          ? 'tmf-plus-user-card glow-card'
                          : 'pack-card glow-card special-package'
                      }`
                      : 'pack-card glow-card special-package'
                  }
                >
                  <Box className='card-header'>
                    {packageRow?.packageTag && (
                      <Typography
                        className='badge'
                        style={{ background: 'linear-gradient(to right, #a855f7, #ec4899)' }}
                      >
                        {packageRow?.packageTag}
                      </Typography>
                    )}
                    <Typography className='title'>{packageRow?.packageName}</Typography>
                  </Box>
                  {hasActiveSubscription && packageRow?.isSubscriberOnly && hasMaxSubscriberOnlyPackage && (
                    <Box className='subscribe-only-package'>
                      <Typography>TMF Plus Exclusive</Typography>
                    </Box>
                  )}
                  <Box className='card-body'>
                    <Typography className='gc-text'>
                      <span className='text-flash'>GC </span>{' '}
                      {packageRow?.gcCoin > 0
                        ? formatPriceWithCommas(formatValueWithB(Number(packageRow?.gcCoin)))
                        : packageRow?.gcCoin}
                    </Typography>

                    <Box className='box-wrap'>
                      <Box className='bonus-box glow-card'>
                        <Typography className='bonus-title glow-text'>
                          <img src={GiftIcon} className='bounce' me={1} />
                          🎁 BONUS 🎁
                        </Typography>
                        <Typography className='bonus-sub text-flash'>
                          +{' '}
                          {packageRow?.scCoin > 0
                            ? formatPriceWithCommas(formatValueWithB(Number(packageRow?.scCoin)))
                            : packageRow?.scCoin}{' '}
                          FREE SWEEPCOINS
                        </Typography>

                        {packageRow?.isScratchCardExist && (
                          <Grid className='extra-bonus text-flash'>
                            + <img src={scratchCardIcon} />{' '}
                            <Typography className='scratch-price' variant='span'>
                              SCRATCH CARD
                            </Typography>
                          </Grid>
                        )}

                        {packageRow?.isFreeSpinExist && (
                          <Grid className='extra-bonus text-flash'>
                            + <img src={scratchCardIcon} />{' '}
                            <Typography className='scratch-price' variant='span'>
                              FREE SPIN
                            </Typography>
                          </Grid>
                        )}
                      </Box>
                      <Box className='buy-wrap'>
                        <Typography className='price glow-text'>
                          {hasActiveSubscription && hasSubscription && packageDiscountPercentage ? (
                            // Case 1: Subscribed & Subscriber-only
                            <>
                              <del className='original-price'>
                                ${formatPriceWithCommas(formatAmount(packageRow?.amount))}
                              </del>{' '}
                              <span className='discounted-price'>
                                $
                                {formatPriceWithCommas(
                                  formatDiscountAmount(
                                    packageRow?.amount,
                                    packageData?.subscriberExclusiveDiscountPercentage
                                  )
                                )}
                              </span>
                            </>
                          ) : hasActiveSubscription &&
                            (hasSubscription || !hasSubscription) &&
                            packageRow?.isSubscriberOnly &&
                            maxPackageDiscountPercentage ? (
                            // Case 2: Not subscribed & Subscriber-only
                            <>$ {formatPriceWithCommas(formatAmount(packageRow?.amount))}</>
                          ) : (
                            // Case 3: Everything else
                            <>$ {formatPriceWithCommas(formatAmount(packageRow?.amount))}</>
                          )}
                        </Typography>
                        <Button
                          variant='contained'
                          className='btn btn-primary glow-card'
                          onClick={() => handleBuyNow(packageRow)}
                          data-tracking={`Store.${packageRow?.packageName}.Offer`}
                          data-tracking-item-id={packageRow?.packageId}
                          data-tracking-item-price={formatAmount(packageRow?.amount)}
                          data-tracking-item-name={`${packageRow?.packageName}`}
                          data-tracking-item-list={'User.Store.Main'}
                          data-tracking-item-catalog={'Special_Package'}
                        >
                          {internalUser && packageRow?.isSubscriberOnly ? (
                            <span>
                              TMF +
                              <LockRoundedIcon />
                            </span>
                          ) : (
                            'BUY NOW'
                          )}
                        </Button>
                      </Box>
                    </Box>
                  </Box>

                  <Box className='card-meta'>
                    <Typography className='viewing'>
                      <img src={outlinedEye} alt='eye' style={{ marginRight: '6px' }} />
                      {packageRow?.liveViewingUsers} people viewing
                    </Typography>
                    <Typography className='purchased'>
                      <span style={{ marginRight: '4px' }}>🟢</span> {packageRow?.todayPurchaseCount} purchased today
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Grid container my={0.5} spacing={1}>
            {packageData?.rows?.length > 0
              ? packageData?.rows
                ?.filter((packageRow) => !packageRow?.isSpecialPackage || packageRow?.isSpecialPackage === true)
                ?.slice(packageRow?.isSpecialPackage ? 1 : 0)
                .map((item, index) => {
                  return (
                    <Grid item xs={12} md={3} key={`package-${item?.packageId}`}>
                      <Grid
                        className={
                          hasActiveSubscription
                            ? `pack-card tmf-plus ${hasSubscription &&
                              item?.isSubscriberOnly &&
                              maxPackageDiscountPercentage &&
                              packageDiscountPercentage
                              ? ''
                              : (hasSubscription || !hasSubscription) &&
                                item?.isSubscriberOnly &&
                                maxPackageDiscountPercentage &&
                                !packageDiscountPercentage
                                ? 'tmf-plus-user-card'
                                : ''
                            }`
                            : 'pack-card'
                        }
                      >
                        <Box className='card-header'>
                          {item?.packageTag && (
                            <p className='badge' style={{ background: colors[index % colors.length] }}>
                              {item?.packageTag}
                            </p>
                          )}
                          <Typography className='title'>{item?.packageName}</Typography>
                        </Box>
                        {hasActiveSubscription && item?.isSubscriberOnly && hasMaxSubscriberOnlyPackage && (
                          <Box className='subscribe-only-package'>
                            <Typography>TMF Plus Exclusive</Typography>
                          </Box>
                        )}
                        <Box className='card-body'>
                          <Typography className='gc-text'>
                            <span className='text-flash'>GC </span>{' '}
                            {item?.gcCoin > 0
                              ? formatPriceWithCommas(formatValueWithB(Number(item?.gcCoin)))
                              : item?.gcCoin}
                          </Typography>

                          <Box className='box-wrap'>
                            <Box className='bonus-box glow-card'>
                              <Typography className='bonus-title glow-text'>
                                <img src={GiftIcon} className='bounce' me={1} />
                                🎁 BONUS 🎁
                              </Typography>
                              <Typography className='bonus-sub text-flash'>
                                +{' '}
                                {item?.scCoin > 0
                                  ? formatPriceWithCommas(formatValueWithB(Number(item?.scCoin)))
                                  : item?.scCoin}{' '}
                                FREE SWEEPCOINS
                              </Typography>

                              {item?.isScratchCardExist && (
                                <Grid className='extra-bonus text-flash'>
                                  + <img src={scratchCardIcon} />{' '}
                                  <Typography className='scratch-price' variant='span'>
                                    SCRATCH CARD
                                  </Typography>
                                </Grid>
                              )}

                                {item?.isFreeSpinExist && (
                                  <Grid className='extra-bonus text-flash'>
                                    + <img src={scratchCardIcon} />{' '}
                                    <Typography className='scratch-price' variant='span'>
                                      FREE SPIN
                                    </Typography>
                                  </Grid>
                                )}
                              </Box>
                              <Box className='buy-wrap'>
                                <Typography className='price glow-text'>
                                  {' '}
                                  {hasActiveSubscription && hasSubscription && packageDiscountPercentage ? (
                                    // Case 1: Subscribed & Subscriber-only
                                    <>
                                      <del className='original-price'>
                                        ${formatPriceWithCommas(formatAmount(item?.amount))}
                                      </del>{' '}
                                      <span className='discounted-price'>
                                        $
                                        {formatPriceWithCommas(
                                          formatDiscountAmount(
                                            item?.amount,
                                            packageData?.subscriberExclusiveDiscountPercentage
                                          )
                                        )}
                                      </span>
                                    </>
                                  ) : hasActiveSubscription &&
                                    (hasSubscription || !hasSubscription) &&
                                    item?.isSubscriberOnly &&
                                    maxPackageDiscountPercentage ? (
                                    // Case 2: Not subscribed & Subscriber-only
                                    <>$ {formatPriceWithCommas(formatAmount(item?.amount))}</>
                                  ) : (
                                    // Case 3: Everything else
                                    <>$ {formatPriceWithCommas(formatAmount(item?.amount))}</>
                                  )}
                                </Typography>
                                <Button
                                  variant='contained'
                                  className='btn btn-primary glow-card'
                                  onClick={() => handleBuyNow(item)}
                                  data-tracking={`Store.${item?.packageName}.Offer`}
                                  data-tracking-item-id={item?.packageId}
                                  data-tracking-item-price={formatAmount(item?.amount)}
                                  data-tracking-item-name={`${item?.packageName}`}
                                  data-tracking-item-list={'User.Store.Main'}
                                  data-tracking-item-catalog={'Basic_Package'}
                                >
                                  {internalUser && item?.isSubscriberOnly ? (
                                    <span>
                                      TMF +
                                      <LockRoundedIcon />
                                    </span>
                                  ) : (
                                    'BUY NOW'
                                  )}
                                </Button>
                              </Box>
                            </Box>
                          </Box>

                          <Box className='card-meta'>
                            <Typography className='viewing'>
                              <img src={outlinedEye} alt='eye' style={{ marginRight: '6px' }} />
                              {packageRow?.liveViewingUsers} people viewing
                            </Typography>
                            <Typography className='purchased'>
                              <span style={{ marginRight: '4px' }}>🟢</span> {packageRow?.todayPurchaseCount} purchased
                              today
                            </Typography>
                          </Box>
                        </B>
                      </Grid>
                    </Grid>
                  )
                })
              : 'No Active Packages '}
          </Grid>

          <Grid className='footer-cta-container'>
            <Box className='footer-cta-content'>
              <Typography variant='h6'>Secure Payment Methods </Typography>
              <Typography className='security-text'>🛡 256-bit SSL Encryption • PCI DSS Compliant</Typography>

              <Box className='logos-row'>
                <Box className='logo-card'>
                  <img src={VisaImg} alt='visa' />
                </Box>
                <Box className='logo-card'>
                  <img src={MasterImg} alt='mastercard' />
                </Box>
                <Box className='logo-card text-logo'>American Express</Box>
                <Box className='logo-card text-logo'>Trustly</Box>
                <Box className='logo-card text-logo'>Skrill</Box>
              </Box>

              <Typography className='join-winners'>
                All transactions are processed securely through our certified payment partners
              </Typography>
            </Box>
          </Grid>
          <Grid className='footer-cta-container'>
            <Box className='footer-cta-content'>
              <Typography variant='h5'>What Our Players Say </Typography>

              <Typography className='rating-name'>
                <Typography className='star-rotate'>
                  <img src={StarIcon} alt='star' />
                  {/* ⭐ */}
                </Typography>{' '}
                <Typography className='star-rotate'>
                  <img src={StarIcon} alt='star' />
                </Typography>{' '}
                <Typography className='star-rotate'>
                  <img src={StarIcon} alt='star' />
                </Typography>{' '}
                <Typography className='star-rotate'>
                  <img src={StarIcon} alt='star' />
                </Typography>{' '}
                <Typography className='star-rotate'>
                  <img src={StarIcon} alt='star' />
                </Typography>{' '}
                <span>4.7/5 from 4,108+ reviews</span>
              </Typography>
              <Swiper
                className='feedback-slider-container'
                spaceBetween={30}
                slidesPerView={1}
                pagination={{ clickable: true }}
                navigation
                loop
                autoplay={{ delay: 3000, disableOnInteraction: false }}
                modules={[Pagination, Navigation, Autoplay]}
                style={{ width: '100%' }}
              >
                {/* <Box> */}
                {testimonialsData.map((data) => (
                  <SwiperSlide key={data.name} className='feedback-box-container'>
                    <Box className='feedback-box'>
                      <img src={data.image} style={{ borderRadius: '50%' }} />
                      <Box className='feedback-content'>
                        <Typography>{data.name}</Typography>

                        <Typography>
                          {data.location}
                          {/* <span className='packageName'>{data.packageName}</span> */}
                        </Typography>

                        <Rating name='rating' value={data?.rating} readOnly />
                        <Typography>{data.text}</Typography>
                      </Box>
                    </Box>
                  </SwiperSlide>
                ))}
              </Swiper>
              {/* </Box> */}
            </Box>
          </Grid>

          <Grid className='footer-cta-container'>
            <Box className='footer-cta-content'>
              <Typography variant='h5'>Why 500,000+ Players Choose The Money Factory?</Typography>
              <Grid className='footer-cta'>
                <Box>
                  <Box className='icon-container bounce'>
                    <LockRoundedIcon sx={{ fontSize: 60 }} />
                  </Box>
                  <Typography variant='h6'>Military-Grade Security</Typography>
                  <Typography>Your data & transactions protected with 256-bit encryption</Typography>
                </Box>
                <Box>
                  <Box className='icon-container bounce'>
                    <BoltOutlinedIcon sx={{ fontSize: 60 }} />
                  </Box>
                  <Typography variant='h6'>Lightning Fast Delivery</Typography>
                  <Typography>Coins in your account within 10 seconds guaranteed</Typography>
                </Box>
                <Box>
                  <Box className='icon-container bounce'>
                    <WorkspacePremiumOutlinedIcon sx={{ fontSize: 60 }} />
                  </Box>
                  <Typography variant='h6'>VIP Treatment</Typography>
                  <Typography>24/7 priority support & exclusive member benefits</Typography>
                </Box>
              </Grid>
              <Grid className='why-choose-tmf'>
                {whyChooseTMFReview.map(({ name, rating, feedback }) => (
                  <Box key={name} className='feedback-box'>
                    <Typography>{feedback}</Typography>
                    <Typography className='rating-name'>
                      <Rating name='rating' value={rating} readOnly /> - <span>{name}</span>
                    </Typography>
                  </Box>
                ))}
              </Grid>
            </Box>
          </Grid>
        </Box>
      </Grid>
    </>
  )
}

export default Package
