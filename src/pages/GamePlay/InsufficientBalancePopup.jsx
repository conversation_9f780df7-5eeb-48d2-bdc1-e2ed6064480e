import React, { useState } from 'react'
import useStyles from './insufficient.style'
import { Box, Button, Grid, Typography, DialogContent, IconButton, CircularProgress } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import BonusBadge from '../../components/ui-kit/icons/svg/bonus.svg'
import FreeTag from '../../components/ui-kit/icons/svg/free.svg'
import Specialbanner from '../../components/ui-kit/icons/svg/specialbanner.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import offerGraphics from '../../components/ui-kit/icons/opImages/offer-graphics.webp'
import smallCoinGraphic from '../../components/ui-kit/icons/opImages/small-coin-graphic.webp'
import usePackages from '../Store/hooks/usePackages'
import { formatPriceWithCommas, formatValueWithB, formatAmount, formatDiscountAmount } from '../../utils/helpers'
import { useGetProfileMutation } from '../../reactQuery'
import StepperForm from '../../components/StepperForm'
import usePaysafePayment from '../Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../../components/Loader/PaymentLoader'
import { usePaymentProcessStore, useUserStore } from '../../store/store'
import scratchCardIcon from '../../components/ui-kit/icons/png/scratch-card-icon.png'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { JoinTMFPlus, TMFPlus } from '../../components/ui-kit/icons/svg'
import LockIcon from '@mui/icons-material/Lock'
import { PlayerRoutes } from '../../routes'
import packageGif from '../../components/ui-kit/icons/gif/package.gif'
import { useNavigate } from 'react-router-dom'
import LazyImage from '../../utils/lazyImage'

const InsufficientBalancePopup = () => {
  const portalStore = usePortalStore()
  const classes = useStyles()
  const navigate = useNavigate()
  const [paymentData, setPaymentData] = useState(null)
  const [packageDetails, setPackageDetails] = useState()
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const cancelDepositOpen = usePaymentProcessStore((state) => state.cancelDepositOpen)

  const { packageData, isloading } = usePackages()
  const packageRow = packageData?.rows?.[0]
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)

  // TMF+ SUBSCRIPTION
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser
  const hasSubscription = userSubscription?.subscriptionDetail !== null && hasActiveSubscription
  const packageDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.PACKAGE_EXCLUSIVE_DISCOUNT
  const maxPackageDiscountPercentage = userSubscription?.subscriptionFeatureMaxValue?.PACKAGE_EXCLUSIVE_DISCOUNT
  const hasSubscriberOnlyPackage = userSubscription?.subscriptionFeatureDetail?.PACKAGE_SUBSCRIBER_ONLY
  const hasMaxSubscriberOnlyPackage = userSubscription?.subscriptionFeatureMaxValue?.PACKAGE_SUBSCRIBER_ONLY

  const setPaymentHandle = (res) => {
    setPaymentData(res)
  }
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setIsPaymentScreenLoading(false)
      portalStore.openPortal(
        () => (
          <>
            <Box className='stepper-outer-box'>
              <StepperForm
                stepperCalledFor={'purchase'}
                packageDetails={packageDetails}
                setPaymentHandle={setPaymentHandle}
              />
            </Box>
          </>
        ),
        'StepperModal'
      )
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const handleBuyNow = (item) => {
    if ((!hasSubscription || hasSubscription) && item?.isSubscriberOnly && maxPackageDiscountPercentage && !packageDiscountPercentage) {
      portalStore.closePortal()
      navigate(PlayerRoutes.Subscriptions)
      return
    }
    setIsPaymentScreenLoading(true)
    ; (function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageDetails(item)

    getProfileMutation.mutate()
  }
  const handleClose = () => {
    setPaymentData(null)
    portalStore.closePortal()
  }

  const CheckSubscription = () => {
    const renderSubscribed = (message) => (
      <Grid>
        <Box className='subscribe-container'>
          <img src={packageGif} className='tmf-gif' alt='gif' />
          <Box className='tmf-plus-exclusive'>
            <Box className='non-subscribe-offers'>{message}</Box>
            <Typography className='non-subscribe-offers'>
              KEEP SPINNING — more surprises are on the way!
            </Typography>
          </Box>
        </Box>
      </Grid>
    )

    const renderNonSubscribed = (buttonText, buttonAction, showUpgrade = false) => (
      <Grid>
        <Box className='non-subscribe-container'>
          <img src={packageGif} className='tmf-gif' alt='gif' />
          <Box className='left-section'>
            <Box className='tmf-plus-exclusive'>
              <Box className='tmf-text'>
                {showUpgrade && <span className='non-subscribe-offers'>UPGRADE</span>}
                <LazyImage
                  src={showUpgrade ? TMFPlus : JoinTMFPlus}
                  alt='TMF+'
                  sizes='100px'
                  priority
                  style={{ cursor: 'pointer', height: '2rem' }}
                />
                <span className='non-subscribe-offers'> TO GET EXCLUSIVE OFFERS.</span>
              </Box>
              <Typography className='non-subscribe-exclusive'>
                Exclusive bonuses and surprises await. One spin could change everything!
              </Typography>
            </Box>
          </Box>

          <Button
            type='button'
            className='btn btn-secondary join-now'
            onClick={() => navigate(buttonAction)}
          >
            {buttonText}
          </Button>
        </Box>
      </Grid>
    )

    // --- SUBSCRIBER CASES ---
    if (hasSubscription) {
      const hasDiscount = Boolean(packageDiscountPercentage)
      const hasExclusive = Boolean(hasSubscriberOnlyPackage)

      // Subscriber with discount
      if (hasDiscount && hasExclusive) {
        return renderSubscribed(
          `YOU’RE ENJOYING ${packageDiscountPercentage}% OFF ON ALL TMF+ DEALS!`
        )
      }

      if (hasDiscount) {
        return renderSubscribed(
          `YOU’RE ENJOYING ${packageDiscountPercentage}% OFF ON EXCLUSIVE TMF+ DEALS!`
        )
      }

      if (hasExclusive) {
        return renderSubscribed('YOU’RE ENJOYING ACCESS ON EXCLUSIVE TMF+ DEALS!')
      }

      // No perks → suggest upgrade if max values exist
      if (maxPackageDiscountPercentage || hasMaxSubscriberOnlyPackage) {
        return renderNonSubscribed('UPGRADE NOW', PlayerRoutes.Subscriptions, true)
      }

      return null
    }

    // --- NON-SUBSCRIBER CASES ---
    if (!hasSubscription) {
      if (maxPackageDiscountPercentage || hasMaxSubscriberOnlyPackage) {
        return renderNonSubscribed('JOIN NOW', PlayerRoutes.Subscriptions)
      }

      return null
    }

    return null
  }

  return (
    <DialogContent>
      <IconButton onClick={handleClose} className='close-btn'>
        <CloseIcon />
      </IconButton>
      {(isPaymentScreenLoading || cancelDepositOpen) && <PaymentLoader />}
      <Grid>
        <Typography variant='h4' className='modal-header'>
          Exclusive Offers Await !
        </Typography>
        <Typography variant='h6' className='modal-subheader'>
          Enjoy immediate benefits with your purchase
        </Typography>
      </Grid>
      <Grid className={classes.lobbyRight}>
        <Box className='package-page'>
          {hasActiveSubscription && <CheckSubscription />}
          {isloading && (
            <div>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
              Loading...
            </div>
          )}
          {packageRow?.isSpecialPackage === true && (
            <Grid className='special-offer-section'>
              <Grid
                className={`package-card tmf-plus ${hasSubscription && packageRow?.isSubscriberOnly && maxPackageDiscountPercentage && packageDiscountPercentage
                  ? ''
                  : (hasSubscription || !hasSubscription) && packageRow?.isSubscriberOnly && maxPackageDiscountPercentage && !packageDiscountPercentage
                    ? 'tmf-plus-user-card'
                    : ''
                  }`}
              >
                {packageRow?.isSubscriberOnly && maxPackageDiscountPercentage && (
                  <Box className='subscribe-only-package'>
                    <Typography>TMF Plus Exclusive</Typography>
                  </Box>
                )}
                <Grid container spacing={0.5}>
                  <Grid item xs={6} lg={3}>
                    <img
                      src={packageRow?.imageUrl ? packageRow?.imageUrl : offerGraphics}
                      alt='Offer'
                      className='offer-graphics'
                    />
                  </Grid>
                  <Grid item xs={12} lg={6} className='package-details-grid'>
                    <Box className={classes.container}>
                      <Grid className='package-details' spacing={0.5} justifyContent='center' alignItems='center'>
                        <Grid className='package-content'>
                          <Grid className='package-price'>
                            <img src={usdchipIcon} alt='coins' />
                            {packageRow?.gcCoin} GC +
                          </Grid>
                          <Grid className='package-price'>
                            <img src={FreeTag} alt='free' className='free-tag' />
                          </Grid>
                          <Grid className='package-price'>
                            <img src={usdIcon} alt='free' />
                            {packageRow?.scCoin} SC
                          </Grid>
                        </Grid>
                        <Button
                          className='btn btn-primary'
                          onClick={() => handleBuyNow(packageRow)}
                          data-tracking={`Store.${packageRow?.packageName}.Offer`}
                          data-tracking-item-id={packageRow?.packageId}
                          data-tracking-item-price={formatAmount(packageRow?.amount)}
                          data-tracking-item-name={`${packageRow?.packageName}`}
                          data-tracking-item-list={'User.Store.Main'}
                          data-tracking-item-catalog={'Special_Package'}
                        >
                          {hasSubscription && packageDiscountPercentage
                            ? (
                              // Case 1: Subscribed & Subscriber-only
                              <>
                                <del className='original-price'>
                                  ${formatAmount(packageRow?.amount)}
                                </del>{' '}
                                <span className='discounted-price'>
                                  ${formatDiscountAmount(packageRow?.amount, packageData?.subscriberExclusiveDiscountPercentage)}
                                </span>
                              </>)
                            : (hasSubscription || !hasSubscription) && packageRow?.isSubscriberOnly && maxPackageDiscountPercentage
                                ? (
                                  // Case 2: Not subscribed & Subscriber-only
                                  <>
                                    TMF+
                                    <LockIcon className='tmf-lock-icon' style={{ height: '1.5rem', marginRight: '0.5rem', marginLeft: '0.5rem', color: 'black' }} />
                                    ${formatAmount(packageRow?.amount)}
                                  </>)
                                : (
                                  // Case 3: Everything else
                                  <>${formatAmount(packageRow?.amount)}</>)}
                        </Button>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={6} lg={3} className='badge-grid'>
                    <Grid className='offer-badge'>
                      <img src={Specialbanner} alt='Offer' />
                      <Grid className='sp-offer-badge-content'>
                        {packageRow?.bonusPercentage > 0 ? (
                          <>
                            <Typography variant='h5'>{packageRow?.bonusPercentage}%</Typography>
                            <Typography>Bonus</Typography>
                          </>
                        ) : (
                          <Typography>SPECIAL OFFER</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Grid className='purchase-section'>
            <Grid className='purchase-section-content'>
              {packageData?.rows?.length > 0
                ? packageData?.rows
                  ?.filter((packageRow) => !packageRow?.isSpecialPackage || packageRow?.isSpecialPackage === true)
                  ?.slice(packageRow?.isSpecialPackage ? 1 : 0)
                  .map((item, index) => {
                    return (
                      <Grid
                        className={`package-card tmf-plus ${hasSubscription && item?.isSubscriberOnly && maxPackageDiscountPercentage && packageDiscountPercentage
                          ? ''
                          : (hasSubscription || !hasSubscription) && item?.isSubscriberOnly && maxPackageDiscountPercentage && !packageDiscountPercentage
                            ? 'tmf-plus-user-card'
                            : ''
                          }`}
                        key={`package-${item?.packageId}`}
                      >
                        {item?.isSubscriberOnly && maxPackageDiscountPercentage && (
                          <Box className='subscribe-only-package'>
                            <Typography>TMF Plus Exclusive</Typography>
                          </Box>)}
                        <Grid container spacing={0.5}>
                          <Grid item xs={6} md={6} lg={3}>
                            <Grid className='package-img-wrap order-1'>
                              <>
                                {item?.bonusPercentage > 0 && (
                                  <Grid className='package-img'>
                                    <img src={BonusBadge} alt='Badge' className='bonus-badge' />
                                    <Grid className='offer-badge-content-text'>
                                      <Typography variant='h5'>{item?.bonusPercentage}%</Typography>
                                      <Typography>Bonus</Typography>
                                    </Grid>
                                  </Grid>
                                )}
                              </>

                              <img
                                src={item?.imageUrl ? item?.imageUrl : smallCoinGraphic}
                                alt='Bonus'
                                className='bonus-graphics'
                              />
                            </Grid>
                          </Grid>
                          <Grid item xs={12} md={12} lg={6} className='order-3'>
                            <Grid className='package-details' spacing={1} justifyContent='center' alignItems='center'>
                              <Grid className='package-content'>
                                <Grid className='package-price'>
                                  <img src={usdchipIcon} alt='coins' />
                                  {item?.gcCoin > 0
                                    ? formatPriceWithCommas(formatValueWithB(Number(item?.gcCoin)))
                                    : item?.gcCoin}{' '}
                                  GC +
                                </Grid>
                                <Grid className='package-price'>
                                  <img src={FreeTag} alt='free' className='free-tag' />
                                </Grid>
                                <Grid className='package-price'>
                                  <img src={usdIcon} alt='free' />
                                  {item?.scCoin > 0
                                    ? formatPriceWithCommas(formatValueWithB(Number(item?.scCoin)))
                                    : item?.scCoin}{' '}
                                  SC
                                </Grid>

                                {item?.isScratchCardExist && <Grid className='package-price extra-prize'>
                                  + <img src={scratchCardIcon} />{' '}
                                  <Typography className='scratch-price' variant='span'>
                                    SCRATCH CARD
                                  </Typography>
                                </Grid>}
                                {item?.isFreeSpinExist &&
                                  <Grid className='package-price extra-prize'>
                                    + <img src={scratchCardIcon} />{' '}
                                    <Typography className='scratch-price' variant='span'>
                                      FREE SPIN
                                    </Typography>
                                  </Grid>
                                }
                              </Grid>
                            </Grid>

                            {item?.isScratchCardExist && <Grid className='package-price mob-extra-prize'>
                              <span style={{ color: '#fff' }}> +</span> <img src={scratchCardIcon} />{' '}
                              <Typography className='scratch-price' variant='span'>
                                SCRATCH CARD
                              </Typography>
                            </Grid>}
                            {item?.isFreeSpinExist && <Grid className='package-price mob-extra-prize'>
                              + <img src={scratchCardIcon} />{' '}
                              <Typography className='scratch-price' variant='span'>
                                FREE SPIN
                              </Typography>
                            </Grid>}

                          </Grid>
                          <Grid item xs={6} md={6} lg={3} className='order-2'>
                            <Grid className='package-btn-wrap'>
                              <Button
                                className='btn btn-primary'
                                onClick={() => handleBuyNow(item)}
                                data-tracking={`Store.${item?.packageName}.Offer`}
                                data-tracking-item-id={item?.packageId}
                                data-tracking-item-price={formatAmount(item?.amount)}
                                data-tracking-item-name={`${item?.packageName}`}
                                data-tracking-item-list={'User.Store.Main'}
                                data-tracking-item-catalog={'Basic_Package'}
                              >
                                {hasSubscription && packageDiscountPercentage
                                  ? (
                                    // Case 1: Subscribed & Subscriber-only
                                    <>
                                      <del className='original-price'>
                                        ${formatAmount(item?.amount)}
                                      </del>{' '}
                                      <span className='discounted-price'>
                                        ${formatDiscountAmount(item?.amount, packageData?.subscriberExclusiveDiscountPercentage)}
                                      </span>
                                    </>)
                                  : (hasSubscription || !hasSubscription) && item?.isSubscriberOnly && maxPackageDiscountPercentage
                                      ? (
                                        // Case 2: Not subscribed & Subscriber-only
                                        <>
                                          TMF+
                                          <LockIcon className='tmf-lock-icon' style={{ height: '1.5rem', marginRight: '0.5rem', marginLeft: '0.5rem', color: 'black' }} />
                                          ${formatAmount(item?.amount)}
                                        </>)
                                      : (
                                        // Case 3: Everything else
                                        <>${formatAmount(item?.amount)}</>)}
                              </Button>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    )
                  })
                : 'No Active Packages '}
            </Grid>
          </Grid>
        </Box>
      </Grid>
    </DialogContent>
  )
}

export default InsufficientBalancePopup
