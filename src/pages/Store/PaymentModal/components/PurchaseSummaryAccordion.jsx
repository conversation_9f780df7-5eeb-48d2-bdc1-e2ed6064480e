import React from 'react'
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { formatPriceWithCommas } from '../../../../utils/helpers'
import usdCash from '../../../../components/ui-kit/icons/utils/usd-cash.webp'
import cardCoin2 from '../../../../components/ui-kit/icons/utils/card-coin2.webp'

const PurchaseSummaryAccordion = ({ packageDetails, discountAmount, finalAmount, baseAmount }) => {
  return (
    <Accordion className='payment-accordion'>
      <AccordionSummary
        className='payment-accordion-summary'
        expandIcon={<ArrowDropDownIcon sx={{ color: 'white !important' }} />}
        aria-controls='panel1-content'
        id='panel1-header'
      >
        <Typography className='payment-accordion-summary-title' component='p'>
          Purchase Summary
        </Typography>
      </AccordionSummary>
      <AccordionDetails className='payment-accordion-details'>
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>SC Coin</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <img src={usdCash} alt='Coin' className='payment-accordion-detail-item-img' />
            <Typography>
              {formatPriceWithCommas(packageDetails?.scCoin || 0)}{' '}
              {discountAmount?.bonusSc > 0 && (
                <span style={{ color: 'green', fontWeight: 700 }}>
                  + {formatPriceWithCommas(discountAmount?.bonusSc)} SC
                </span>
              )}
            </Typography>
          </Box>
        </Box>
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>GC Coin</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <img src={cardCoin2} alt='Coin' className='payment-accordion-detail-item-img' />
            <Typography>
              {formatPriceWithCommas(packageDetails?.gcCoin || 0)}{' '}
              {discountAmount?.bonusGc > 0 && (
                <span style={{ color: 'green', fontWeight: 700 }}>
                  + {formatPriceWithCommas(discountAmount?.bonusGc)} GC
                </span>
              )}
            </Typography>
          </Box>
        </Box>
        {baseAmount && (
          <>
            <Box className='payment-accordion-detail-item'>
              <Typography className='payment-accordion-detail-item-title' component='p'>Base Amount</Typography>
              <Typography className='order-total discount payment-accordion-detail-item-content'>
                ${formatPriceWithCommas(baseAmount?.toFixed(2)) || 0.0}
              </Typography>
            </Box>
          </>
        )}
        {discountAmount?.discount > 0 && (
          <>
            <Box className='payment-accordion-detail-item'>
              <Typography className='payment-accordion-detail-item-title' component='p'>Amount</Typography>
              <Typography className='order-total discount payment-accordion-detail-item-content'>
                ${formatPriceWithCommas((discountAmount?.discount + discountAmount?.amount).toFixed(2)) || 0.0}
              </Typography>
            </Box>
            <Box className='payment-accordion-detail-item'>
              <Typography sx={{ color: 'green !important', fontWeight: 700 }} component='p'>Discount</Typography>
              <Typography sx={{ color: 'green !important', fontWeight: 700 }} className='order-total discount'>
                - ${formatPriceWithCommas(discountAmount?.discount.toFixed(2)) || 0.0}
              </Typography>
            </Box>
          </>
        )}
        {baseAmount && baseAmount !== finalAmount && (
          <Box className='payment-accordion-detail-item'>
            <Typography sx={{ color: 'green !important', fontWeight: 700 }} className='payment-accordion-detail-item-title-bonus' component='p'>TMF+ Discount</Typography>
            <Typography sx={{ color: 'green !important', fontWeight: 700 }} className='order-total discount payment-accordion-detail-item-content-bonus'>
              -${formatPriceWithCommas((baseAmount - finalAmount)?.toFixed(2)) || 0.0}
            </Typography>
          </Box>
        )}
        <Box className='payment-accordion-detail-item total-amount'>
          <Typography sx={{ color: 'white !important', fontWeight: '600' }} component='p'>Total Amount</Typography>
          <Typography>
            <b>${formatPriceWithCommas(finalAmount?.toFixed(2))}</b>
          </Typography>
        </Box>
      </AccordionDetails>
    </Accordion>
  )
}

export default PurchaseSummaryAccordion
