import React, { useState, useEffect } from 'react'
import {
  Button,
  Grid,
  IconButton,
  Typography,
  DialogContent,
  Box,
  OutlinedInput,
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment
} from '@mui/material'
import { Close as CloseIcon, Visibility, VisibilityOff } from '@mui/icons-material'
import PropTypes from 'prop-types'
import Select from 'react-select'
import useStyles from './style'
import { usePortalStore } from '../../store/userPortalSlice'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import {
  useVaultDetailsMutation,
  useDepositVaultCoinsMutation,
  useWithdrawVaultCoinsMutation,
  useGetProfileMutation
} from '../../reactQuery'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import { PlayerRoutes } from '../../routes'
import VaultDescription from './VaultDescription'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
// import LazyImage from '../../utils/lazyImage'
import vaultGif2 from '../../components/ui-kit/icons/gif/vault-2.gif'
import LazyImage from '../../utils/lazyImage'
import { TMFPlus } from '../../components/ui-kit/icons/svg'
import { useUserStore } from '../../store/useUserSlice'

function CustomTabPanel (props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  }
}

const coinOptions = [
  { value: 'gc', label: 'GC coins' },
  { value: 'sc', label: 'SC coins' }
]

const staticErrorMsg = 'Amount must be greater than 0.'

const Vault = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [coinType, setCoinType] = useState({ value: 'sc', label: 'SC coins' })
  const [depositAmount, setDepositAmount] = useState(null)
  const [withdrawAmount, setWithdrawAmount] = useState(null)
  const [value, setValue] = useState(0)
  const [depositErrorMsg, setDepositErrorMsg] = useState('')
  const [withdrawErrorMsg, setWithdrawErrorMsg] = useState('')
  const [maxDepositAmount, setMaxDepositAmount] = useState(0)
  const [maxWithdrawAmount, setMaxWithdrawAmount] = useState(0)
  const [is2FAEnabled, setIs2FAEnabled] = useState(false)
  const [code2FA, setCode2FA] = useState(null)
  const [loginPassword, setLoginPassword] = useState('')
  const [isSocialLogin, setSocialLogin] = useState('0')
  const [isLoading, setIsLoading] = useState(false)
  const [showVaultDescription, setShowVaultDescription] = useState(false)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser

  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const navigate = useNavigate()

  const handleClose = () => {
    portalStore.closePortal()
  }
  useEffect(() => {
    if (showVaultDescription) {
      portalStore.openPortal(() => <VaultDescription />, 'valutModal')
    }
  }, [showVaultDescription])

  const handleTabChange = (event, newValue) => {
    setValue(newValue)
    setWithdrawAmount('')
    setDepositAmount('')
    setDepositErrorMsg('')
    setWithdrawErrorMsg('')
    setCode2FA('')
  }

  const handleMouseDownPassword = (event) => {
    event.preventDefault()
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setSocialLogin(res?.data?.data?.signInMethod)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })

  const { mutate: getVaultDetails, data: vaultData } = useVaultDetailsMutation({
    onSuccess: (data) => {
      setMaxDepositAmount(data?.data?.data?.maxGcAllowForDeposit)
      setMaxWithdrawAmount(data?.data?.data?.maxGcAllowForWithdraw)
      setIs2FAEnabled(data?.data?.data?.authEnable)
      setCoinType({ value: 'gc', label: 'GC coins' })
    },
    onError: (error) => {
      console.error('Error fetching vault details:', error)
    }
  })

  const { mutate: depositVaultCoins } = useDepositVaultCoinsMutation({
    onSuccess: (data) => {
      toast.success(`${data?.data?.message}`)
      setDepositAmount()
      getVaultDetails()
      setCode2FA('')
      setIsLoading(false)
    },
    onError: (error) => {
      console.error('Error depositing coins:', error)
      setIsLoading(false)
    }
  })

  const { mutate: withdrawVaultCoins } = useWithdrawVaultCoinsMutation({
    onSuccess: (data) => {
      toast.success(`${data?.data?.message}`)
      setWithdrawAmount('')
      setLoginPassword('')
      setCode2FA('')
      getVaultDetails()
      setIsLoading(false)
    },
    onError: (error) => {
      console.error('Error withdrawing coins:', error)
      setIsLoading(false)
    }
  })

  useEffect(() => {
    getVaultDetails()
    getProfileMutation.mutate()
  }, [])

  const handleMaxClick = () => {
    const maxDepositCoins =
      coinType.value === 'gc'
        ? vaultData?.data?.data?.maxGcAllowForDeposit
        : vaultData?.data?.data?.maxScAllowForDeposit
    const maxWithdrawCoins =
      coinType.value === 'gc'
        ? vaultData?.data?.data?.maxGcAllowForWithdraw
        : vaultData?.data?.data?.maxScAllowForWithdraw
    setDepositAmount(maxDepositCoins)
    setWithdrawAmount(maxWithdrawCoins)
    setMaxDepositAmount(maxDepositCoins)
    setMaxWithdrawAmount(maxWithdrawCoins)
    setDepositErrorMsg('')
    setWithdrawErrorMsg('')
  }

  const handleDepositClick = () => {
    if (depositAmount <= 0) {
      toast.error('Deposit amount must be greater than 0.')
      return
    }

    if (depositAmount > maxDepositAmount) {
      toast.error('You cannot deposit more than the allowed amount')
      return
    }

    const deposit = coinType.value
    if (is2FAEnabled) {
      depositVaultCoins({ token: code2FA, coinType: deposit, amount: depositAmount })
      setIsLoading(true)
    } else {
      depositVaultCoins({ coinType: deposit, amount: depositAmount, token: code2FA })
      setIsLoading(true)
    }
  }

  const handleWithdrawClick = () => {
    if (withdrawAmount <= 0) {
      toast.error('Withdraw amount must be greater than 0.')
      return
    }

    const withdraw = coinType.value

    if (!withdraw || !withdrawAmount || (isSocialLogin === '0' && !loginPassword)) {
      toast.error('Please fill in all mandatory fields.')
      return
    }

    if (withdrawAmount > maxWithdrawAmount) {
      toast.error('You cannot withdraw more than the vault balance.')
      return
    }

    const payload = {
      coinType: withdraw,
      amount: parseFloat(withdrawAmount),
      password: btoa(loginPassword),
      token: is2FAEnabled ? code2FA : null
    }

    if (is2FAEnabled && !code2FA) {
      toast.error('2FA code is required.')
      return
    }

    withdrawVaultCoins(payload)
    setIsLoading(true)
  }

  const handleEnable2FAClick = () => {
    navigate(PlayerRoutes.Settings, { state: { tabIndex: 1 } })
    handleClose()
  }

  const handleAmountChange = (e) => {
    const inputAmount = parseFloat(e.target.value)
    if (inputAmount <= 0) {
      if (e.target.name === 'depositAmount') {
        setDepositErrorMsg(staticErrorMsg)
        setWithdrawErrorMsg('')
      } else {
        setWithdrawErrorMsg(staticErrorMsg)
        setDepositErrorMsg('')
      }
      return
    }
    setDepositErrorMsg('')
    setWithdrawErrorMsg('')
    setDepositAmount(inputAmount)
    setWithdrawAmount(inputAmount)
  }

  const handleCointTypeChange = (selectedOption) => {
    const maxDepositCoins =
      selectedOption.value === 'gc'
        ? vaultData?.data?.data?.maxGcAllowForDeposit
        : vaultData?.data?.data?.maxScAllowForDeposit
    const maxWithdrawCoins =
      selectedOption.value === 'gc'
        ? vaultData?.data?.data?.maxGcAllowForWithdraw
        : vaultData?.data?.data?.maxScAllowForWithdraw

    setMaxDepositAmount(maxDepositCoins)
    setMaxWithdrawAmount(maxWithdrawCoins)
    setCoinType(selectedOption)
    setDepositAmount('')
    setWithdrawAmount('')
    setDepositErrorMsg('')
    setWithdrawErrorMsg('')
  }

  const handleShowVaultDescription = () => {
    setShowVaultDescription(true)
  }

  function useCountdownToUTC (utcTimeStr) {
    const [timeLeft, setTimeLeft] = useState('')

    useEffect(() => {
      function calculateTime () {
        const [time, meridiem] = utcTimeStr.split(' ')
        const [hourStr, minuteStr] = time.split('.')

        let hours = parseInt(hourStr, 10)
        const minutes = parseInt(minuteStr, 10)

        // Convert to 24-hour format
        if (meridiem.toUpperCase() === 'PM' && hours !== 12) hours += 12
        if (meridiem.toUpperCase() === 'AM' && hours === 12) hours = 0

        const now = new Date()
        const target = new Date(Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          hours,
          minutes
        ))

        // If target passed today, use tomorrow
        if (target <= now) {
          target.setUTCDate(target.getUTCDate() + 1)
        }

        const diffInMs = target - now
        const totalSeconds = Math.floor(diffInMs / 1000)
        const hrs = Math.floor(totalSeconds / 3600)
        const mins = Math.floor((totalSeconds % 3600) / 60)
        const secs = totalSeconds % 60

        return [hrs, mins, secs]
          .map(num => num.toString().padStart(2, '0'))
          .join(':')
      }

      // Set immediately
      setTimeLeft(calculateTime())

      // Update every second
      const timer = setInterval(() => {
        setTimeLeft(calculateTime())
      }, 1000)

      return () => clearInterval(timer)
    }, [utcTimeStr])

    return timeLeft
  }

  const CheckSubscription = () => {
    const maxInterestPercentage = userSubscription?.subscriptionFeatureMaxValue?.VAULT_INTEREST_RATE
    const interestPercentage = userSubscription?.subscriptionFeatureDetail?.VAULT_INTEREST_RATE
    const hasSubscription = userSubscription?.subscriptionDetail !== null
    const hasInterest = Boolean(interestPercentage)
    const hasMaxInterest = Boolean(maxInterestPercentage)

    if (hasSubscription && hasMaxInterest && hasInterest) {
      return (
        <>
          <Box className='vault-tmf-plus-subscribe'>
            <Typography className='tmf-plus-plan'>NEXT PAYOUT IN:</Typography>
            <Typography className='tmf-plus-plan-time'>
              {useCountdownToUTC('9.30 AM UTC')}
            </Typography>
          </Box>
          <Typography className='ex-tmf'>
            <img src={vaultGif2} className='tmf-gif' alt='gif' />
            <Box>
              <Typography className='guaranteed-content'>
                Your {vaultData?.data?.data?.totalVaultScCoin !== 0 ? `${vaultData?.data?.data?.totalVaultScCoin} SC` : ''} Vault is growing {interestPercentage}%<br />
                richer—like magic, but real!
              </Typography>
            </Box>
          </Typography>
        </>
      )
    }

    if ((hasSubscription && hasMaxInterest && !hasInterest) || (!hasSubscription && hasMaxInterest)) {
      return (
        <Box
          className='vault-tmf-plus-non-subscribe'
          onClick={() => {
            handleClose()
            navigate(PlayerRoutes.Subscriptions)
          }}
        >
          <img src={vaultGif2} className='tmf-gif' alt='gif' />
          <Box>
            <Typography className='guaranteed-content'>
              LET YOUR VAULT BLOOM! JOIN OR UPGRADE{' '}
              <LazyImage
                src={TMFPlus}
                alt='TMF+'
                priority
                style={{ cursor: 'pointer', height: '1rem' }}
              />{' '}
            </Typography>
            <Typography className='guaranteed-content'>
              AND GROW YOUR COINS BY UP TO {maxInterestPercentage}% INTEREST.
            </Typography>
          </Box>
        </Box>
      )
    }

    return null
  }

  return (
    <Grid className=''>
      <DialogContent className={classes.valutModal}>
        <Box className={classes.redeemModalContainer}>
          <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
            <Typography variant='h4' className='modal-title'>
              Vault
            </Typography>
            <Grid className='modal-close'>
              <IconButton edge='start' color='inherit' className='close' onClick={handleClose} aria-label='close'>
                <CloseIcon />
              </IconButton>
            </Grid>
          </Grid>
          <Box className='modal-tabs'>
            <Box>
              <Tabs value={value} onChange={handleTabChange} aria-label='basic tabs example'>
                <Tab label='Balance' {...a11yProps(0)} />
                <Tab label='Deposit' {...a11yProps(1)} />
                <Tab label='Withdraw' {...a11yProps(2)} />
              </Tabs>
            </Box>

            <CustomTabPanel value={value} index={0}>
              {hasActiveSubscription && <CheckSubscription />}
              <Grid className='balance-tab'>
                <Grid className='balance-details'>
                  {/* <Typography variant='h4'> Available Balance</Typography> */}
                  <Typography>Available Balance</Typography>
                </Grid>
                <Grid className='balance-card-wrap'>
                  <Grid className='balance-card'>
                    <Typography>{vaultData?.data?.data?.vaultGcCoin} GC </Typography>
                    <img src={usdchipIcon} alt='Icon' />
                  </Grid>
                  <Grid className='balance-card'>
                    <Typography>{vaultData?.data?.data?.totalVaultScCoin} SC </Typography>
                    <img src={usdIcon} alt='Icon' />
                  </Grid>
                </Grid>

                {!is2FAEnabled && (
                  <Grid className='two-fector'>
                    <Typography>Improve your account security with Two-Factor Authentication</Typography>
                    {/* <Link href='javascript:void(0)'>
                    Learn more about Vault
                  </Link> */}
                  </Grid>
                )}
                <Grid className='vault-description-wrap'>
                  <a href='#' className='vault-link' onClick={handleShowVaultDescription}>
                    Learn more about Vault
                  </a>
                </Grid>
              </Grid>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
              {hasActiveSubscription && <CheckSubscription />}
              <Grid>
                <Grid className='theme-select'>
                  <Typography id='provider-select-label'>Select Coin</Typography>
                  <Grid className='theme-select-content'>
                    <Select
                      options={coinOptions}
                      classNamePrefix='reactInnerCoinSelect'
                      value={coinType}
                      onChange={(selectedOption) => handleCointTypeChange(selectedOption)}
                    />
                    {coinType.value === 'gc' ? (
                      <img src={usdchipIcon} alt='Chip' className='select-coin' />
                    ) : (
                      <img src={usdIcon} alt='Chip' className='select-coin' />
                    )}
                  </Grid>
                </Grid>
                <Grid className='amount-input'>
                  <Grid className='amount-input-text'>
                    <Typography>
                      Amount <span style={{ color: 'red' }}>*</span>
                    </Typography>
                    <Typography>{`Max: ${maxDepositAmount} Coins`}</Typography>
                  </Grid>

                  <OutlinedInput
                    id='outlined-basic'
                    label=''
                    className='no-spinners'
                    variant='outlined'
                    placeholder='Enter Amount'
                    name='depositAmount'
                    type='number'
                    value={depositAmount ?? ''}
                    onChange={handleAmountChange}
                    onKeyDown={(evt) => ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()}
                  />
                  <Grid className='input-cta'>
                    {coinType.value === 'gc' ? (
                      <img src={usdchipIcon} alt='Chip' className='select-coin' />
                    ) : (
                      <img src={usdIcon} alt='Chip' className='select-coin' />
                    )}
                    <Button type='button' onClick={handleMaxClick}>
                      Max
                    </Button>
                  </Grid>
                  {depositErrorMsg && <p className='inputError'>{depositErrorMsg}</p>}
                </Grid>

                {is2FAEnabled && (
                  <Grid className='amount-input'>
                    <OutlinedInput
                      id='outlined-adornment-otp'
                      type='text'
                      value={code2FA}
                      onChange={(e) => setCode2FA(e.target.value)}
                      placeholder='Enter 2FA Code'
                    />
                  </Grid>
                )}
                <Grid className='modal-btn'>
                  <Button type='button' className='btn btn-primary' onClick={handleDepositClick} disabled={isLoading}>
                    Deposit to Vault {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                  </Button>
                </Grid>
                {!is2FAEnabled && (
                  <Grid className='two-fector'>
                    <Typography>Improve your account security with Two-Factor Authentication</Typography>
                    <Button type='button' className='btn btn-primary' onClick={handleEnable2FAClick}>
                      Enable 2FA
                    </Button>
                    {/* <Link href='javascript:void(0)'>
                    Learn more about Vault
                  </Link> */}
                  </Grid>
                )}
                <Grid className='vault-description-wrap'>
                  <a href='#' className='vault-link' onClick={handleShowVaultDescription}>
                    Learn more about Vault
                  </a>
                </Grid>
              </Grid>
            </CustomTabPanel>

            <CustomTabPanel value={value} index={2}>
              {hasActiveSubscription && <CheckSubscription />}
              <Grid>
                <Grid className='theme-select'>
                  <Typography id='provider-select-label'>Select Coin </Typography>
                  <Grid className='theme-select-content'>
                    <Select
                      options={coinOptions}
                      classNamePrefix='reactInnerCoinSelect'
                      value={coinType}
                      onChange={(selectedOption) => handleCointTypeChange(selectedOption)}
                    />
                    {coinType.value === 'gc' ? (
                      <img src={usdchipIcon} alt='Chip' className='select-coin' />
                    ) : (
                      <img src={usdIcon} alt='Chip' className='select-coin' />
                    )}
                  </Grid>
                </Grid>
                <Grid className='amount-input'>
                  <Grid className='amount-input-text'>
                    <Typography>
                      Amount <span style={{ color: 'red' }}>*</span>
                    </Typography>
                    <Typography>{`Max: ${maxWithdrawAmount} Coins`}</Typography>
                  </Grid>

                  <OutlinedInput
                    id='outlined-basic'
                    label=''
                    min='1'
                    className='no-spinners'
                    variant='outlined'
                    placeholder='Enter Amount'
                    name='withdrawAmount'
                    type='number'
                    value={withdrawAmount ?? ''}
                    onChange={handleAmountChange}
                    inputProps={{ min: 1 }}
                    onKeyDown={(evt) => ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()}
                  />
                  <Grid className='input-cta'>
                    {coinType.value === 'gc' ? (
                      <img src={usdchipIcon} alt='Chip' className='select-coin' />
                    ) : (
                      <img src={usdIcon} alt='Chip' className='select-coin' />
                    )}
                    {/* <   img src={usdchipIcon} alt='Chip' className='select-coin' /> */}
                    <Button type='button' onClick={handleMaxClick}>
                      Max
                    </Button>
                  </Grid>
                  {withdrawErrorMsg && <p className='inputError'>{withdrawErrorMsg}</p>}
                </Grid>
                <Grid className='amount-input'>
                  {isSocialLogin === '0' && (
                    <>
                      <Grid className='amount-input-text'>
                        <Typography>
                          Password <span style={{ color: 'red' }}>*</span>
                        </Typography>
                      </Grid>

                      <OutlinedInput
                        id='outlined-adornment-password'
                        type={showPassword ? 'text' : 'password'}
                        value={loginPassword}
                        onChange={(e) => setLoginPassword(e.target.value)}
                        endAdornment={
                          <InputAdornment position='end'>
                            <IconButton
                              aria-label='toggle password visibility'
                              onClick={() => setShowPassword(!showPassword)}
                              onMouseDown={handleMouseDownPassword}
                              edge='end'
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        }
                        placeholder='Enter login password'
                      />
                    </>
                  )}
                </Grid>

                {is2FAEnabled && (
                  <Grid className='amount-input'>
                    <OutlinedInput
                      id='outlined-adornment-otp'
                      type='text'
                      value={code2FA}
                      onChange={(e) => setCode2FA(e.target.value)}
                      placeholder='Enter 2FA Code'
                    />
                  </Grid>
                )}
                <Grid className='modal-btn'>
                  <Button type='button' className='btn btn-primary' onClick={handleWithdrawClick} disabled={isLoading}>
                    Withdraw from Vault {isLoading && <CircularProgress size={24} style={{ marginLeft: 8 }} />}
                  </Button>
                </Grid>
                {!is2FAEnabled && (
                  <Grid className='two-fector'>
                    <Typography>Improve your account security with Two-Factor Authentication</Typography>
                    <Button type='button' className='btn btn-primary' onClick={handleEnable2FAClick}>
                      Enable 2FA
                    </Button>

                    {/* <Link href='javascript:void(0)'>
                    Learn more about Vault
                  </Link> */}
                  </Grid>
                )}
                <Grid className='vault-description-wrap'>
                  <a href='#' className='vault-link' onClick={handleShowVaultDescription}>
                    Learn more about Vault
                  </a>
                </Grid>
              </Grid>
            </CustomTabPanel>
          </Box>
        </Box>
      </DialogContent>
    </Grid>
  )
}

export default Vault
