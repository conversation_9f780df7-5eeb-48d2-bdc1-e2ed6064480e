import { makeStyles } from '@mui/styles'

import ValuteBg from '../../components/ui-kit/icons/png/valut-modal-bg.png'

export default makeStyles((theme) => ({
  valutModal: {
    background: theme.colors.valutModal,
    minWidth: theme.spacing(32.25),
    borderRadius: theme.spacing(0.5625),
    overflowX: 'hidden',
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      minWidth: '100%'
    },
    '&:before': {
      position: 'absolute',
      content: "''",
      width: '100%',
      height: '100%',
      backgroundImage: `url(${ValuteBg})`,
      backgroundSize: 'cover',
      top: 0
    },
    '& .modal-close': {
      color: theme.colors.textWhite,
      marginLeft: 'auto',
      position: 'absolute',
      right: theme.spacing(-1),
      top: theme.spacing(-1),
      cursor: 'pointer',
      zIndex: '5'
    },

    '& .modal-tabs': {
      marginTop: theme.spacing(2),
      padding: theme.spacing(0, 2),
      borderRadius: theme.spacing(1.875),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.625, 0)
      },
      '& .MuiTabs-flexContainer': {
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(1.875),
        '& .MuiButtonBase-root': {
          borderRadius: theme.spacing(1.875),
          color: theme.colors.textWhite,
          background: theme.colors.inputBg,
          //
          fontSize: theme.spacing(1.125),
          fontWeight: '700',
          flex: '1',
          '&.Mui-selected': {
            background: theme.colors.modalTabBtnActive
          }
        }
      },
      '& .MuiTabs-indicator': {
        display: 'none'
      },
      '& .MuiBox-root': {
        padding: theme.spacing(1, 0)
      },
      '& .vault-tmf-plus-non-subscribe': {
        background: 'linear-gradient(86.38deg, #1B181E 9.68%, #044309 98.47%)',
        border: '0.93px solid #FDB72E',
        boxShadow: '0px 3.72px 3.72px 0px #FDB72E4D',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        opacity: '1',
        borderRadius: '9.29px',
        marginTop: '1rem',
        marginBottom: '1rem',
        padding: theme.spacing(0.5),
        [theme.breakpoints.down('sm')]: {
          padding: theme.spacing(0.25)
        },
        '& .guaranteed-content': {
          marginTop: '0 !important',
          borderWidth: '0.43px',
          fontWeight: 700,
          fontSize: theme.spacing(1.18),
          background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.8)
          },
          '& .tmf-plus': {
            fontWeight: '700',
            color: ' #FDB72E'
          },
          '& .join-now': {
            fontWeight: '600 !important',
            color: '#0077FF !important',
            cursor: 'pointer'
          }
        },
        '& .tmf-gif': {
          width: '5rem',
          filter: 'drop-shadow(0 0 20px #044309)',
          [theme.breakpoints.down('sm')]: {
            width: '4rem'
          }
        }
      },
      '& .vault-tmf-plus-subscribe': {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        opacity: '1',
        borderRadius: '10px',
        marginTop: '1rem',
        padding: theme.spacing(0.5, 1),
        width: 'fit-content',
        textTransform: 'uppercase',
        marginLeft: 'auto',
        marginRight: 'auto',
        '& .tmf-plus-plan': {
          fontWeight: 700,
          fontSize: theme.spacing(1.5),
          background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0px 4px 4px rgba(253, 183, 46, 0.6)', // ✨ shadow for text
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.8)
          }
        },
        '& .tmf-plus-plan-time': {
          margin: '0 0.5rem',
          fontWeight: 700,
          fontSize: theme.spacing(1.5),
          background: 'linear-gradient(180deg, #00FF11 30.56%, #00A30B 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0px 4px 4px rgba(0, 163, 11, 0.6)', // ✨ green glow
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.8)
          }
        }
      },
      '& .ex-tmf': {
        background: 'linear-gradient(86.38deg, #1B181E 9.68%, #044309 98.47%)',
        border: '0.93px solid #FDB72E',
        boxShadow: '0px 3.72px 3.72px 0px #FDB72E4D',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        opacity: '1',
        borderRadius: '10px',
        marginBottom: '1rem',
        padding: theme.spacing(0.5, 1),
        width: 'fit-content',
        textTransform: 'uppercase',
        marginLeft: 'auto',
        marginRight: 'auto',
        fontWeight: '500 !important',
        fontSize: theme.spacing(1.05),
        textAlign: 'center',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '& .guaranteed-content': {
          marginTop: '0 !important',
          borderWidth: '0.43px',
          fontWeight: 700,
          fontSize: theme.spacing(1.18),
          background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(0.8)
          },
          '& .tmf-plus': {
            fontWeight: '700',
            color: ' #FDB72E'
          },
          '& .join-now': {
            fontWeight: '600 !important',
            color: '#0077FF !important',
            cursor: 'pointer'
          }
        },
        '& .tmf-gif': {
          width: '5rem',
          filter: 'drop-shadow(0 0 20px #044309)',
          [theme.breakpoints.down('sm')]: {
            width: '4rem'
          }
        }
      }
    },
    '& .theme-select': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1),
      '& .theme-select-content': {
        position: 'relative',
        width: '100%',
        '& .reactInnerCoinSelect__control': {
          width: '100%',
          height: theme.spacing(3.5),
          background: theme.colors.inputBg,
          borderRadius: theme.spacing(0.625),
          border: 'none',
          paddingLeft: theme.spacing(2.2),
          '& div': {
            color: theme.colors.textWhite
          }
        },
        '& .css-t3ipsp-control': {
          boxShadow: '0 0 0 1px #FDB72E'
        },
        '& .reactInnerCoinSelect__menu': {
          background: theme.colors.inputBg,
          color: theme.colors.textWhite,

          '& .reactInnerCoinSelect__option': {
            '&.reactInnerCoinSelect__option--is-focused, &.reactInnerCoinSelect__option--is-selected': {
              background: theme.colors.YellowishOrange,
              color: theme.colors.textBlack
            }
          }
        }
      },

      '& p': {
        color: theme.colors.textWhite,

        fontWeight: '600',
        minWidth: theme.spacing(6)
      },
      '& .MuiInputBase-root': {
        borderRadius: theme.spacing(0.625),
        flex: 1,
        minWidth: '300px',
        [theme.breakpoints.down('sm')]: {
          minWidth: 'unset'
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderRadius: theme.spacing(0.625)
            // borderColor:"red",
          }
        }
      },
      '& .select-coin': {
        position: 'absolute',
        left: theme.spacing(0.625),
        top: theme.spacing(1)
      },
      '& .MuiFormLabel-root ': {
        marginLeft: theme.spacing(2)
      }
    },
    '& .amount-input': {
      position: 'relative',
      margin: theme.spacing(2, 0),

      '& .MuiInputBase-root': {
        width: '100%',
        paddingRight: '0',
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(0),
        '& input': {
          width: '100%',
          // background:theme.colors.inputBg,
          // borderRadius:theme.spacing(0.625),
          color: theme.colors.textWhite,

          height: theme.spacing(1.125)
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderRadius: theme.spacing(0.625),
            borderColor: theme.colors.YellowishOrange
          }
        },
        '& .MuiInputAdornment-root': {
          position: 'absolute',
          right: theme.spacing(1),

          '& .MuiButtonBase-root': {
            color: theme.colors.textWhite
          }
        }
      },
      '& .input-cta': {
        position: 'absolute',
        right: '2px',
        top: theme.spacing(2.15),
        display: 'flex',
        gap: theme.spacing(1),
        '& .MuiButtonBase-root': {
          background: theme.colors.modalTabBtnActive,
          borderRadius: theme.spacing(0.625),

          fontWeight: '500',
          minHeight: theme.spacing(3.125),
          color: theme.colors.textWhite
        }
      },
      '& .amount-input-text': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: theme.spacing(0, 1, 0.625),
        '& p': {
          color: theme.colors.landingFooterText,
          fontSize: theme.spacing(1),
          fontWeight: '600'
        }
      },
      '& .MuiInputBase-root-MuiOutlinedInput-root': {
        '&:hover': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent'
          }
        }
      },
      '& .inputError': {
        color: theme.colors.error,
        fontSize: `${theme.spacing(0.8)}!important`,
        margin: '0 !important',
        lineHeight: 'normal !important',
        minHeight: '16px',
        fontWeight: '600'
      }
    },
    '& .modal-btn': {
      '& button': {
        width: '100%',
        fontSize: theme.spacing(1.25),
        color: theme.colors.textBlack,

        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.875)
        }
      }
    },
    '&  .balance-tab': {
      '& .balance-details': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: theme.spacing(1),
        '& h4': {
          fontSize: theme.spacing(1.4375),
          fontWeight: '700',
          color: theme.colors.textWhite
        },
        '& p': {
          fontSize: theme.spacing(0.875),
          color: theme.colors.valutMOdalText,
          fontWeight: '500'
        }
      },
      '& .balance-card-wrap': {
        // display:"flex",
        gap: theme.spacing(1),
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing(1),
        '& .balance-card': {
          display: 'flex',
          gap: theme.spacing(1),
          justifyContent: 'space-between',
          alignItems: 'center',
          background: theme.colors.inputBg,
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.625),
          width: '100%',
          marginBottom: '10px'
        }
      },
      '& p': {
        fontSize: theme.spacing(0.875),
        color: theme.colors.textWhite
      },
      '& .two-fector': {
        '& p': {
          margin: theme.spacing(1, 0)
        }
      }
    },
    '& .two-fector': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      margin: theme.spacing(1, 0),
      '& .MuiButtonBase-root': {
        width: '100%',
        fontSize: theme.spacing(1.25),
        // color:theme.colors.textBlack,

        margin: theme.spacing(0.625, 0),
        color: '#000',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },
      '& p': {
        color: theme.colors.landingFooterText,
        fontSize: theme.spacing(1),
        fontWeight: '600'
      },
      '& a': {
        textDecoration: 'none'
      }
    },
    '& .vault-description-wrap': {
      textAlign: 'center',
      marginTop: '0.75rem'
    },
    '& .vault-link': {
      fontWeight: 700,
      fontSize: '20px',
      color: 'blue',
      textDecoration: 'none',
      cursor: 'pointer'
    },
    '& .vault-close-icon': {
      position: 'absolute',
      top: 5,
      right: 20,
      color: theme.colors.textWhite
    },
    '& .vault-detail-wrap': {
      padding: theme.spacing(2, 4),
      '& .vault-description': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.125),
        fontWeight: 700,
        lineHeight: '22.97px',
        marginBottom: theme.spacing(1)
      }
    },
    '& .vault-banner': {
      width: '100%'
    },
    '& .modal-title': {
      fontSize: theme.spacing(1.75),
      fontWeight: '700',

      color: theme.colors.textWhite,
      textAlign: 'center',
      width: '100%'
    }
  }
}))
