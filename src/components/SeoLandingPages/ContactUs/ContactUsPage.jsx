import React from 'react'
// import LandingHeader from '../LandingHeader'
import SeoLandingFooter from '../SeoLandingFooter'
import useStyles from './style'
import { Grid, Button, Typography, Box, Link, OutlinedInput, FormControl } from '@mui/material'
import { Email, LocationOn, Phone } from '@mui/icons-material'
import contactfb from '../../../components/ui-kit/icons/svg/contact-fb.svg'
import contactig from '../../../components/ui-kit/icons/svg/contact-ig.svg'
import contactx from '../../../components/ui-kit/icons/svg/contact-x.svg'
import contacttg from '../../../components/ui-kit/icons/svg/contact-tg.svg'
import { useForm } from 'react-hook-form'
import { useContactUsInfo } from '../../../reactQuery'
import toast from 'react-hot-toast'
import contactUsSchema from './Schema'
import { yupResolver } from '@hookform/resolvers/yup'
import LandingHeader from '../../../pages/Landing/LandingHeader'
import SeoHead from '../../../utils/seoHead'
const ContactUsPage = () => {
  const classes = useStyles()
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      fullName: '',
      email: '',
      message: ''
    },
    resolver: yupResolver(contactUsSchema),
    mode: 'onChange'
  })

  const contactUsData = useContactUsInfo({
    onSuccess: (res) => {
      if (res.data.success) {
        toast.success('Message Sent')
        reset()
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  const onSubmit = (data) => {
    contactUsData.mutate({
      fullName: data.fullName,
      email: data.email,
      message: data.message
    })
  }

  return (
    <Grid style={{ width: '100%' }}>
      <Box className={classes.contactusWrapper}>
        <Box className='contact-wrap'>
          <LandingHeader />
          <SeoHead
            title='Contact Us | The Money Factory – Social Casino Support'
            description='Need help or have questions? Contact The Money Factory team via phone or email. We’re here Monday to Friday to support your social casino experience.  '
            // keywords={['about us', 'company', 'mission', 'team']}
            // url='https://www.themoneyfactory.com/contact-us'
            // imageUrl='https://testttyourdomain.com/images/about-us.jpg'
          />
          <Typography variant='h1'>Contact Us</Typography>
          <Typography variant='h2'>We'd love to hear from you!</Typography>
          <Typography variant='body1' className='head-p'>
            Whether you have a question, need support, or just want to share your thoughts, feel free to reach out.
          </Typography>

          <Grid container spacing={5}>
            {/* Contact Info Section */}
            <Grid item xs={12} md={5}>
              <Box className='support-box'>
                <Box className='point-wrap'>
                  {/* <Box className='icon-wrap'>
                    <Phone />
                  </Box>
                  <Box>
                    <Typography variant='h5'>
                      <Link href='tel:+***********' underline='hover' color='inherit'>
                        ******-9873256
                      </Link>
                    </Typography>
                    <Typography variant='body1'>
                      Our customer service team is available Monday to Friday, from 9 AM to 5 PM EST. Call us anytime
                      during business hours, and we’ll be happy to assist you.
                    </Typography>
                  </Box> */}
                </Box>
                <Box className='point-wrap'>
                  <Box className='icon-wrap'>
                    <Email />
                  </Box>
                  <Box>
                    <Typography variant='h5'>
                      <Link href='mailto:<EMAIL>' underline='hover' color='inherit'>
                        <EMAIL>
                      </Link>
                    </Typography>
                    <Typography variant='body1'>
                      For non-urgent inquiries or if you'd prefer to email us, send your message to email
                    </Typography>
                  </Box>
                </Box>
                <Box className='point-wrap'>
                  <Box className='icon-wrap'>
                    <LocationOn />
                  </Box>
                  <Box>
                    <Typography variant='h5'>The Money Factory <br/> 8 The Green #17655 <br/>Dover, DE 19901</Typography>
                   
                  </Box>
                </Box>
                <Box className='point-wrap'>
                  <Box className='icon-wrap'>
                    <Phone />
                  </Box>
                  <Box>
                    
                    <Typography variant='h5'>(*************</Typography>
                  </Box>
                </Box>
                <Box className='follow-wrap'>
                  <Typography variant='h5'>Follow Us</Typography>
                  <Box className='contact-icons'>
                    <Link href='https://www.facebook.com/themoneyfactorycasino?mibextid=LQQJ4d' target='_blank'>
                      <img src={contactfb} alt='Facebook' />
                    </Link>
                    <Link href='https://t.me/TheMoneyFactoryUS' target='_blank'>
                      <img src={contacttg} alt='Telegram' />
                    </Link>

                    <Link href='https://x.com/TMFcasino?s=03' target='_blank'>
                      <img src={contactx} alt='Twitter' />
                    </Link>

                    <Link
                      href='https://www.instagram.com/themoneyfactory/?igsh=OTA0NjFscDMyZ3gz&utm_source=qr'
                      target='_blank'
                    >
                      <img src={contactig} alt='Instagram' />
                    </Link>
                  </Box>
                </Box>
              </Box>
            </Grid>

            {/* Contact Form */}
            <Grid item xs={12} md={7}>
              <Box className='contact-box'>
                <Typography variant='h5'>Send Us a Message!</Typography>
                <form onSubmit={handleSubmit(onSubmit)} style={{ display: 'flex', flexDirection: 'column' }}>
                  <FormControl variant='outlined'>
                    <OutlinedInput
                      id='fullName'
                      placeholder='Enter Your Full Name'
                      type='text'
                      className='contact-input'
                      variant='outlined'
                      {...register('fullName')}
                    />
                    <Typography className={classes.errorLabel}>
                      {errors?.fullName && errors?.fullName?.message}
                    </Typography>
                  </FormControl>
                  <FormControl variant='outlined'>
                    <OutlinedInput
                      id='email'
                      placeholder='Enter Email Address'
                      type='text'
                      variant='outlined'
                      className='contact-input'
                      {...register('email')}
                    />
                    <Typography className={classes.errorLabel}>{errors?.email && errors?.email?.message}</Typography>
                  </FormControl>
                  <FormControl variant='outlined'>
                    <OutlinedInput
                      id='message'
                      placeholder='Your Message'
                      className='contact-input'
                      variant='outlined'
                      multiline
                      rows={7}
                      {...register('message')}
                    />
                    <Typography className={classes.errorLabel}>
                      {errors?.message && errors?.message?.message}
                    </Typography>
                  </FormControl>
                  <Button type='submit' variant='contained' className='btn btn-primary'>
                    Send Message
                  </Button>
                </form>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <SeoLandingFooter />
    </Grid>
  )
}

export default ContactUsPage
