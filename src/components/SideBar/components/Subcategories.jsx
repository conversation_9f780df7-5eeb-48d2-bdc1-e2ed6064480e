import React, { useEffect } from 'react'
import '../../../../src/App.css'
import { Link, Typography, Grid } from '@mui/material'
import {
  allGamesHeaderImage,
  defaultCategory,
  headerLobby
} from '../../ui-kit/icons/svg'

import { useNavigate } from 'react-router-dom'
import { useGamesStore } from '../../../store/store'
import { SubCategoryConstants } from '../constants'
import { CasinoQuery } from '../../../reactQuery'

const SubCategories = ({ setIsMobNav }) => {
  const navigate = useNavigate()
  const subCategories = useGamesStore((state) => state.subCategories)
  const setSubCategories = useGamesStore((state) => state.setSubCategories)
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  const selectedSubCat = useGamesStore((state) => state.selectedSubCat)
   const { data: subCategoriesData} = CasinoQuery.getSubcategoryListQuery({ params: {} })
   useEffect(()=>{
     setSubCategories(subCategoriesData?.data)
   },[subCategoriesData])
  const handleSubCategoryData = (key) => {
    setIsMobNav(false)
    navigate('/')
    setSelectedSubCat(key)
  }
  return (
    <Grid className='sidebar-accordian'>
      <Link
        onClick={() => {
          setIsMobNav(false)
          handleSubCategoryData('Lobby')
        }}
        className={selectedSubCat === 'Lobby' ? 'active' : 'Inactive'}
      >
        <img src={headerLobby} alt='Lobby header icon' height='100%' width='100%' />
        <Typography>Lobby</Typography>
      </Link>
      <Link
        onClick={() => {
          setIsMobNav(false)
          handleSubCategoryData(SubCategoryConstants.ALL_GAMES)
        }}
        className={selectedSubCat === SubCategoryConstants.ALL_GAMES ? 'active' : 'Inactive'}
      >
        <img src={allGamesHeaderImage} alt='Casino games header' height='100%' width='100%' />
        <Typography>All Games</Typography>
      </Link>
      {subCategories?.map((subcategory) => {
        const { name, masterGameSubCategoryId, imageUrl, subCategorySlug } = subcategory
        if (subCategorySlug) return null
        return (
          <Link
            key={masterGameSubCategoryId} onClick={() => handleSubCategoryData(name)}
            className={selectedSubCat === name ? 'active' : 'Inactive'}
          >
            <img src={imageUrl?.thumbnail || defaultCategory} alt='Recent played' height='100%' width='100%' />
            <Typography> {name} </Typography>
          </Link>
        )
      })}
    </Grid>
  )
}

export default SubCategories
