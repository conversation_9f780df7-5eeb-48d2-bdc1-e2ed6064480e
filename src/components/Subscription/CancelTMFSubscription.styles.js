import { makeStyles } from '@mui/styles'

export default makeStyles((theme) => ({
  innerModalHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1.5),
    borderBottom: `1px solid ${theme.palette.divider}`,

    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(1),
      '& h4': {
        fontSize: theme.spacing(1.25)
      }
    }
  },
  modalClose: {
    display: 'flex',
    alignItems: 'center'
  },
  paymentStatusModal: {
    padding: theme.spacing(3),

    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(2)
    }
  },
  paymentStatusContent: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing(2),

    [theme.breakpoints.down('sm')]: {
      gap: theme.spacing(1.5),
      '& h6': {
        fontSize: theme.spacing(0.8)
      }
    }
  },

  // Dropdown & TextField Styling
  textField: {
    marginTop: theme.spacing(1),
    width: '100%',
    color: theme.palette.common.white,
    '& .MuiInputBase-input': {
      color: theme.palette.common.white,
      fontSize: theme.spacing(1.25)
    },
    '& .MuiInputLabel-root': {
      color: theme.palette.grey[400],
      fontSize: theme.spacing(1.25),
      '&.Mui-focused': {
        color: theme.palette.common.white
      }
    },
    '& .MuiSelect-select': {
      color: 'white'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: theme.palette.grey[500]
      },
      '&:hover fieldset': {
        borderColor: theme.palette.grey[300]
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.palette.common.white
      }
    },
    // Style the dropdown arrow
    '& .MuiSvgIcon-root': {
      color: theme.palette.grey[300]
    },

    [theme.breakpoints.down('sm')]: {
      marginTop: theme.spacing(0.5),
      '& .MuiInputBase-input': {
        fontSize: theme.spacing(1)
      }
    }
  },

  // Buttons wrapper styling
  buttonsWrapper: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: theme.spacing(1.5),
    marginTop: theme.spacing(2),

    [theme.breakpoints.down('sm')]: {
      alignItems: 'stretch',
      gap: theme.spacing(1)
    },

    '& button': {
      minWidth: 100,

      [theme.breakpoints.down('sm')]: {
        width: '100%'
      }
    }
  },

  menuItem: {
    color: theme.palette.common.white,
    backgroundColor: theme.palette.background.paper,
    '&:hover': {
      backgroundColor: theme.palette.grey[700]
    }
  }
}))
