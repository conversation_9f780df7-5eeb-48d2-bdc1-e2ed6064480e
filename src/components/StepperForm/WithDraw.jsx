import React, { useState, useEffect } from 'react'
import { Grid, Typography, Box } from '@mui/material'
import { useUserStore } from '../../store/useUserSlice'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { bankRedeemSchema, redeemSchema, trustlyRedeemSchema } from './schema/redeemSchema'
import useTransaction from '../../pages/Accounts/hooks/useRedeemRequests'
import useStyles from './StepperForm.styles'
import { PaymentQuery, useGetProfileMutation } from '../../reactQuery'
import ErrorPopup from '../ErrorPopup'
import { usePortalStore } from '../../store/userPortalSlice'
import RedeemInitiated from './PaymentInitiated'
import TransactionSection from '../../pages/Accounts/components/RedeemRequests'
import { customEvent } from '../../utils/optimoveHelper'
import SelectRedeemMethod from './SelectedRedeemMethod'
import BankRedeemForm from './BankRedeemForm'
import SkrillRedeemForm from './SkrillRedeemForm'
import SumSubKycVL5 from './SumSubKycVL5'
import GeocomplyPopup from '../../Context/GeocomplyPopup'
import useSeon from '../../utils/useSeon'
import { useNavigate } from 'react-router-dom'
import TrustlyRedeemForm from './TrustlyRedeemForm'
import TagManager from 'react-gtm-module'
import useEquationValidator from './hook/useEquationValidator'
import { PlayerRoutes } from '../../routes'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import tmfGif from '../../../src/components/ui-kit/icons/gif/tmf-plus.gif'
import tmfPlus from '../../../src/components/ui-kit/icons/svg/TMF-Plus.svg'
import joinTmfPlus from '../../../src/components/ui-kit/icons/svg/JoinTMFPlus.svg'
/* eslint-disable multiline-ternary */

const WithDraw = (props) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [type, setType] = useState('redeem')
  const [isLoading, setIsLoading] = useState(false)
  const [isDisable, setIsDisable] = useState(false)
  const userDetails = useUserStore((state) => state.userDetails)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const schemaRedeem = redeemSchema(userDetails?.minRedeemableCoins, userDetails?.maxRedeemableCoins)
  const bankSchemaRedeem = bankRedeemSchema(userDetails?.minRedeemableCoins, userDetails?.maxRedeemableCoins)
  const trustlySchemaRedeem = trustlyRedeemSchema(userDetails?.minRedeemableCoins, userDetails?.maxRedeemableCoins)
  const [showHistory, setShowHistory] = useState(false)
  const [redeemMethod, setRedeemMethod] = useState('')
  const [isRedeemInitiated, setIsRedeemInitiated] = useState(false)
  const [selectedRedeemMethod, setSelectedRedeemMethod] = useState(props.selectedRedeemMethod || '')
  const kycStatus = useUserStore((state) => state.kycStatus)
  const setKycStatus = useUserStore((state) => state.setKycStatus)
  const { randomEquation, userAnswer, setUserAnswer, isEquationCorrect, pickRandomEquation, handleAnswerSubmit } =
    useEquationValidator()
  // const [secondKyc, setSecondKyc] = useState(false)
  const [token, setToken] = useState('')
  const [userId, setUserId] = useState(null)

  const [showVL5, setShowVL5] = useState(true)
  const sessionId = useSeon()
  const navigate = useNavigate()
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const internalUser = useUserStore((state) => state.userDetails?.isInternalUser)
  const hasActiveSubscription = userSubscription?.isSubscriptionActive && internalUser

  useEffect(() => {
    if (
      (import.meta.env.VITE_NODE_ENV === 'production' || import.meta.env.VITE_NODE_ENV === 'staging') &&
      !userDetails?.isInternalUser
    ) {
      setShowVL5(false)
    } else {
      setShowVL5(true)
    }
  }, [userDetails])

  const handleRedeemMethodClick = (method) => {
    setSelectedRedeemMethod(method) // Update the selected method
  }

  const handleClose = () => {
    portalStore.closePortal()
    navigate('/')
  }

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
    setValue,
    setError,
    reset,
    watch,
    control: skrillControl
  } = useForm({
    resolver: yupResolver(schemaRedeem),
    defaultValues: {
      amount: '',
      actionableEmail: ''
    }
  })

  const {
    handleSubmit: handleBankSubmit,
    register: registerBank,
    formState: { errors: errorsBank },
    setValue: setValueBank,
    setError: setErrorBank,
    reset: resetBank,
    control
  } = useForm({
    resolver: yupResolver(bankSchemaRedeem),
    defaultValues: {
      amount: '',
      bankAccount: ''
    }
  })

  const {
    handleSubmit: handleTrustlySubmit,
    register: registerTrustly,
    formState: { errors: errorsTrustly },
    setValue: setValueTrustly,
    setError: setErrorTrustly,
    reset: resetTrustly,
    control: controlTrustly
  } = useForm({
    resolver: yupResolver(trustlySchemaRedeem),
    defaultValues: {
      amount: '',
      trustlyAccount: ''
    }
  })

  const handleGeocomplyPopup = (data) => {
    portalStore.openPortal(() => <GeocomplyPopup open={true} errorData={data} />, 'tournamentEndPopup')
  }
  const { transactionsData } = useTransaction()
  const isCanadianUser = transactionsData?.isCanadianCustomer ? true : false
  const mutation = PaymentQuery.initPayRedeemMutation({
    onSuccess: (res) => {
      if (res?.data?.kycData) {
        toast.error('Please complete further verification to proceed')
        setKycStatus(true)
        setToken(res?.data?.kycData.token)
        setUserId(res?.data?.kycData.userId)
      } else {
        setIsLoading(false)
        setIsDisable(false)
        getProfileMutation.mutate()
        setIsRedeemInitiated(true)
      }
    },
    onError: (err) => {
      if (err?.length > 0) {
        // const { errors } = err?.response?.data
        err.forEach((error) => {
          if (error?.description) {
            if (error?.errorCode === 3075) {
              if (redeemMethod === 'fiat') {
                portalStore.openPortal(() => <ErrorPopup message={error?.description} />, 'innerModal')
              } else {
                portalStore.openPortal(
                  () => <ErrorPopup message='You need to purchase a package first, to create a redeem request' />,
                  'innerModal'
                )
              }
            } else if (error?.errorCode === 3076) {
              portalStore.openPortal(
                () => (
                  <ErrorPopup message='Please wait to request a redemption until your previous request has been completed.' />
                ),
                'innerModal'
              )
            } else {
              // toast.error(error?.description);
              handleClose()
            }
          }
        })
      } else if (
        err?.response?.data?.data?.state === 'DECLINE' ||
        err?.response?.data?.data?.ipDetails?.vpn === true ||
        err?.response?.data?.data?.ipDetails?.web_proxy === true
      ) {
        localStorage.setItem('allowedUserAccess', false)
        handleClose()
        handleGeocomplyPopup(err?.response?.data?.data)
      }
    }
  })

  const setRedeemAmountValue = (setValueMethod) => {
    if (userDetails?.userWallet?.scCoin?.wsc > userDetails?.maxRedeemableCoins) {
      setValueMethod('amount', userDetails?.maxRedeemableCoins)
    } else {
      setValueMethod('amount', userDetails?.userWallet?.scCoin?.wsc || '0.00')
    }
  }

  const setAmountValue = () => setRedeemAmountValue(setValue)
  const setBankAmountValue = () => setRedeemAmountValue(setValueBank)
  const setTrustlyAmountValue = () => setRedeemAmountValue(setValueTrustly)

  const handleRedeemSubmit = (data, providerType) => {
    const minAmount = parseFloat(userDetails?.minRedeemableCoins)
    const maxAmount = parseFloat(userDetails?.userWallet?.scCoin?.wsc)

    if (minAmount > parseFloat(data?.amount)) {
      const errorMsg = `You can redeem minimum SC ${minAmount} coins`

      if (providerType === 'SKRILL') {
        setError('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      } else if (providerType === 'PAY_BY_BANK') {
        setErrorBank('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      } else if (providerType === 'TRUSTLY') {
        setErrorTrustly('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      }
      return
    }

    if (maxAmount < parseFloat(data?.amount)) {
      const errorMsg = 'Insufficient Redeemable SC coins'

      if (providerType === 'SKRILL') {
        setError('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      } else if (providerType === 'PAY_BY_BANK') {
        setErrorBank('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      } else if (providerType === 'TRUSTLY') {
        setErrorTrustly('amount', { type: 'focus', message: errorMsg }, { shouldFocus: true })
      }
      return
    }

    setIsDisable(true)
    setIsLoading(true)

    const commonParameters = {
      amount: data?.amount,
      sessionKey: sessionId,
      rtyuioo: sessionId === ' ' ? true : false
    }

    const providerSpecificParameters =
      providerType === 'SKRILL'
        ? {
            paymentType: 'redeem',
            providerType: 'SKRILL',
            skrillEmailAddress: data?.actionableEmail
          }
        : providerType === 'TRUSTLY'
        ? {
            paymentType: 'redeem',
            providerType: 'TRUSTLY',
            trustlyTransactionId: data?.trustlyAccount
          }
        : {
            paymentType: 'redeem',
            providerType: 'PAY_BY_BANK',
            bankAccountId: data?.bankAccount
          }
    const mutationData = { ...commonParameters, ...providerSpecificParameters }

    TagManager.dataLayer({
      dataLayer: {
        event: 'redeem',
        ...data
      }
    })
    const optimoveParams =
      providerType === 'SKRILL'
        ? {
            paymentMethod: 'SKRILL',
            amount: data?.amount
          }
        : providerType === 'TRUSTLY'
        ? {
            paymentMethod: 'TRUSTLY',
            amount: data?.amount
          }
        : {
            paymentMethod: 'PAY_BY_BANK',
            amount: data?.amount
          }

    if (isCanadianUser) {
      const isCorrect = handleAnswerSubmit()
      if (!isCorrect) return
    }
    mutation.mutate(mutationData)

    if (import.meta.env.VITE_NODE_ENV === 'production') {
      customEvent('redeem', optimoveParams, userDetails?.userId)
    }
  }

  // Function to prevent invalid characters while allowing backspace and delete
  const preventInvalidCharacters = (evt) => {
    const invalidChars = ['e', 'E', '+', '-', ' '] // Disallow these characters
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Enter'] // Allow these keys
    const key = evt.key
    const isDecimal = key === '.'
    const isDigit = /^[0-9]$/.test(key) // Check if it's a digit
    const value = evt.target.value
    const hasDecimal = evt.target.value.includes('.')

    if (
      invalidChars.includes(key) ||
      (!isDigit && !isDecimal && !allowedKeys.includes(key)) ||
      (isDecimal && hasDecimal)
    ) {
      evt.preventDefault()
    }

    // Allow control keys
    if (allowedKeys.includes(key)) {
      return // Allow deletion and navigation
    }

    // Restrict to 4 decimal places
    if (hasDecimal) {
      const decimalIndex = value.indexOf('.')
      const decimalPart = value.substring(decimalIndex + 1)
      const cursorPos = evt.target.selectionStart

      // Block input if already 2 digits after decimal and cursor is after decimal
      if (decimalPart.length >= 2 && cursorPos > decimalIndex) {
        evt.preventDefault()
        return
      }
    }
  }

  const handleBack = (resetFormFunction) => {
    resetFormFunction({
      amount: '',
      actionableEmail: '', // Reset for Skrill
      bankAccount: '', // Reset for Bank
      checkbox: false
    })
    setSelectedRedeemMethod('')
  }

  const handleKycClose = () => {
    setKycStatus(!kycStatus)
  }

  function formatHoursToDays (hours) {
    if (hours < 24) {
      return `${hours % 24} HOUR${hours % 24 !== 1 ? 'S' : ''}`
    } else {
      const days = Math.floor(hours / 24)
      return `${days} DAY${days !== 1 ? 'S' : ''}`
    }
  }

  const CheckSubscription = () => {
    const approvedDays = userSubscription?.subscriptionFeatureDetail?.GUARANTEED_REDEMPTION_APPROVED_TIME
    const maxApprovedDays = userSubscription?.subscriptionFeatureMaxValue?.GUARANTEED_REDEMPTION_APPROVED_TIME
    const hasSubscription = userSubscription?.subscriptionDetail !== null

    if (hasSubscription && maxApprovedDays && approvedDays) {
      return (
        <Box className='redeem-tmf-plus-subscribe'>
          <img src={tmfGif} className='tmf-gif' alt='gif' />
          <Box>
            <Typography className='guaranteed-content'>
              CONGRATULATIONS! YOUR REDEMPTION APPROVE
            </Typography>
            <Typography className='guaranteed-content'>
              TIME IS JUST {formatHoursToDays(approvedDays)}.
            </Typography>
          </Box>
        </Box>
      )
    } else if (!hasSubscription && maxApprovedDays && !approvedDays) {
      return (
        <Box
          className='redeem-tmf-plus-non-subscribe'
          onClick={() => {
            handleClose()
            navigate(PlayerRoutes.Subscriptions)
          }}
        >
          <img src={tmfGif} className='tmf-gif' alt='gif' />
          <Box>
            <Typography className='guaranteed-content'>
              SKIP THE WAIT{' '}
              <span>
                <img src={joinTmfPlus} alt='TMF+' />
              </span>{' '}
              – APPROVAL
            </Typography>
            <Typography className='guaranteed-content'>
              IN JUST {formatHoursToDays(maxApprovedDays)}.
            </Typography>
          </Box>
        </Box>
      )
    } else if (hasSubscription && maxApprovedDays && !approvedDays) {
      return (
        <Box
          className='redeem-tmf-plus-non-subscribe'
          onClick={() => {
            handleClose()
            navigate(PlayerRoutes.Subscriptions)
          }}
        >
          <img src={tmfGif} className='tmf-gif' alt='gif' />
          <Box>
            <Typography className='guaranteed-content'>
              SKIP THE WAIT{' '} BY UPGRADING
              <span>
                <img src={tmfPlus} alt='TMF+' />
              </span>{' '} PLAN
            </Typography>
            <Typography className='guaranteed-content'>
              – APPROVAL IN JUST {formatHoursToDays(maxApprovedDays)}.
            </Typography>
          </Box>
        </Box>
      )
    } else {
      return null
    }
  }

  return showVL5 && kycStatus ? (
    <SumSubKycVL5 handleClose={handleKycClose} token={token} userId={userId} />
  ) : !isRedeemInitiated ? (
    <>
      {(userDetails?.kycStatus === 'K4' || userDetails?.kycStatus === 'K5') && showHistory ? (
        <TransactionSection showHistory={showHistory} setShowHistory={setShowHistory} />
      ) : (
        <Grid className={classes.StepperModal}>
          <Grid>
            <Box className='verification-code-container'>
              <Typography className='verification-code-text'>
                {isCanadianUser ? 'REDEEM YOUR PRIZE' : 'REDEEM'}
              </Typography>
            </Box>
            {hasActiveSubscription && <CheckSubscription />}
          </Grid>

          {selectedRedeemMethod === '' && (
            <SelectRedeemMethod
              handleRedeemMethodClick={handleRedeemMethodClick}
              handleClose={handleClose}
              setShowHistory={setShowHistory}
              transactionsData={transactionsData}
              isDisable={isDisable}
              userDetails={userDetails}
              selectedRedeemMethod={selectedRedeemMethod}
            />
          )}

          {selectedRedeemMethod === 'skrill' && (
            <form onSubmit={handleSubmit((data) => handleRedeemSubmit(data, 'SKRILL'))} autoComplete='off'>
              <SkrillRedeemForm
                userDetails={userDetails}
                preventInvalidCharacters={preventInvalidCharacters}
                register={register}
                watch={watch}
                setValue={setValue}
                setAmountValue={setAmountValue}
                errors={errors}
                isDisable={isDisable}
                handleBack={() => handleBack(reset)}
                handleClose={handleClose}
                control={skrillControl}
                isCanadianUser={isCanadianUser}
                {...(isCanadianUser && {
                  randomEquation,
                  userAnswer,
                  setUserAnswer,
                  isEquationCorrect,
                  pickRandomEquation
                })}
              />
            </form>
          )}

          {selectedRedeemMethod === 'bank' && (
            <form onSubmit={handleBankSubmit((data) => handleRedeemSubmit(data, 'PAY_BY_BANK'))} autoComplete='off'>
              <BankRedeemForm
                userDetails={userDetails}
                preventInvalidCharacters={preventInvalidCharacters}
                errors={errorsBank}
                handleBack={() => handleBack(resetBank)}
                isDisable={isDisable}
                register={registerBank}
                setBankAmountValue={setBankAmountValue}
                control={control}
              />
            </form>
          )}

          {selectedRedeemMethod === 'trustly' && (
            <form onSubmit={handleTrustlySubmit((data) => handleRedeemSubmit(data, 'TRUSTLY'))} autoComplete='off'>
              <TrustlyRedeemForm
                userDetails={userDetails}
                preventInvalidCharacters={preventInvalidCharacters}
                errors={errorsTrustly}
                handleBack={() => handleBack(resetTrustly)}
                isDisable={isDisable}
                register={registerTrustly}
                setBankAmountValue={setTrustlyAmountValue}
                control={controlTrustly}
                isCanadianUser={isCanadianUser}
                {...(isCanadianUser && {
                  randomEquation,
                  userAnswer,
                  setUserAnswer,
                  isEquationCorrect,
                  pickRandomEquation
                })}
              />
            </form>
          )}
        </Grid>
      )}
    </>
  ) : (
    <RedeemInitiated handleClose={handleClose} type={type} />
  )
}

export default WithDraw
