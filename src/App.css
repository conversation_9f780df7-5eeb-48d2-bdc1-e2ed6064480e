#root {
  /* background-image: url('../src/components/ui-kit/icons/png/body-bg.png'); */
  background-attachment: fixed !important;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.MuiDialogContent-root {
  /* background-image: url('../src/components/ui-kit/icons/png/body-bg.png'); */
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.contact-input input:-webkit-autofill {
  background-color: transparent !important;
  box-shadow: none !important;
  font-size: 1rem;
  /* Optional: to change the text color */
}

input:-webkit-autofill {
  background-color: #3f3f3f !important;
  /* Optional: to change the text color */
}

input:-webkit-autofill::first-line {
  color: white !important;
  /* Optional: to change the text color */
}

.loader {
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Spinner animation for lazy loading */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Screen reader only class for accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.MuiPaper-root {
  background-color: #000 !important;
  box-shadow: none !important;
  border-radius: 10px !important;

  @media (max-width: 400px) {
    margin: 0.625rem !important;
  }
}

.loader img {
  width: 150px;
}

.spinner-wrap {
  margin-bottom: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.loader-img {
  width: 50px;
}

.MuiModal-backdrop {
  backdrop-filter: blur(10px);
}

.btn-gradient.MuiButtonBase-root:hover:before {
  -webkit-animation-name: stripe-bg;
  -webkit-animation-duration: 0.6s;
  -webkit-animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-name: stripe-bg;
  animation-duration: 0.6s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

@-webkit-keyframes stripe-bg {
  from {
    -webkit-transform: translateX(0);
  }

  to {
    -webkit-transform: translateX(26px);
  }
}

@keyframes stripe-bg {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(26px);
  }
}

html,
body {
  overflow-x: hidden;
}

body {
  font-family: 'Rajdhani', sans-serif;
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
  background: #0c0a0e !important;
  height: 100%;
  color: white;
}

.pulse {
  animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0px rgba(203, 184, 186, 0.9);
  }

  100% {
    box-shadow: 0 0 0 20px rgba(203, 184, 186, 0);
  }
}

.glow-text{
  animation: textGlow 2s ease-in-out infinite
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(253, 183, 46, .6);
}

50% {
    text-shadow: 0 0 20px rgba(253, 183, 46, .9);
}
}

.text-flash{
 
    animation: textFlash 1s ease-in-out infinite;

}

@keyframes textFlash {
  0%, 100% {
    opacity: 1;
}

50% {
    opacity: .7;
}
}

.glow-card {
  animation: glow-card 2s infinite;
}
@keyframes glow-card {
  0% {
    box-shadow: rgba(253, 183, 46, 0.3) 0px 0px 20px;
  }

  50% {
    box-shadow: rgba(253, 183, 46, 0.6) 0px 0px 40px;
  }
  100% {
    box-shadow: rgba(253, 183, 46, 0.3) 0px 0px 20px;
  }
}

.star-rotate {
  animation: star-rotate 1.5s ease-in-out infinite;
  display: inline-block;
}

@keyframes star-rotate {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
}


/* .main-page {
  height: 100vh;
} */

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0); /* Start and end at original position */
  }
  40% {
    transform: translateY(-8px); /* Jump up */
  }
  60% {
    transform: translateY(-5px); /* Small bounce */
  }
}

.bounce {
  display: inline-block;
  animation: bounce 2s infinite;
}


#menu-phoneCode .MuiPaper-rounded {
  background-color: #000;
  box-shadow: none;
}

#menu-phoneCode .MuiPaper-rounded li.MuiMenuItem-root {
  color: #fff;
  font-weight: bold;
}

#menu-phoneCode .MuiPaper-rounded li.MuiMenuItem-root.Mui-selected {
  color: #000;
  background-color: #fdb72e;
}

.MuiPickersPopper-root .MuiPickersPopper-paper {
  background-color: #1b181e;
  box-shadow: none !important;

  & .MuiPickersCalendarHeader-label {
    color: #fff;
  }

  & .MuiPickersDay-root {
    color: #fff !important;
  }

  & .MuiPickersCalendarHeader-labelContainer button svg {
    color: #fff;
  }

  & .MuiPickersArrowSwitcher-root button svg {
    color: #fff;
  }

  & .MuiPickersDay-root.Mui-selected {
    color: #000;
    background-color: #fdb72e;
  }

  & .MuiPickersYear-yearButton {
    color: #fff;
  }

  & .MuiPickersYear-yearButton.Mui-selected {
    color: #000;
    background-color: #fdb72e;
  }

  & .Mui-disabled {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

.Mui-error .MuiOutlinedInput-notchedOutline {
  border: 0 !important;
  border-color: transparent !important;
}

.load-more {
  text-align: center;
}

.MuiCircularProgress-svg {
  color: #fff !important;
}

.lobby-filter-left::-webkit-scrollbar {
  height: 0;
  width: 0;
}

::placeholder {
  color: #fff;
  opacity: 0.4;
  /* Firefox */
}

.store-coin {
  width: 20px;
  height: 20px;
}

::-ms-input-placeholder {
  /* Edge 12-18 */
  color: #fff;
}

.game-play-cta-right .MuiSwitch-root .MuiSwitch-track {
  background-color: #292760;
}

.game-play-cta-right .MuiSwitch-root .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background: linear-gradient(180deg, #ffd958 -30.88%, #ff964e 83.82%);
}

.game-play-cta-right .MuiTypography-body1 {
  color: #bfbfc0;
}

.game-play-cta-right .MuiSwitch-root .MuiSwitch-switchBase.Mui-checked .MuiSwitch-thumb {
  color: #131230;
}

.ribbon-pop {
  padding: 1px 8px;
  position: absolute;
  top: 16px;
  right: 1px;
  font-weight: 700;
  text-align: center;
  border-top-right-radius: 3px;
  background: #db1717;
  font-size: 0.875rem;
  color: #fff;
  z-index: 2;
}

.ribbon-pop:before {
  position: absolute;
  background: url(/src/components/ui-kit/icons/png/ribbon-arrow.png);
  content: '';
  left: -5px;
  top: 0;
  width: 30px;
  background-size: contain;
  height: 100%;
  background-repeat: no-repeat;
}

.custom-switch-btn {
  position: relative;
  display: block;
  width: 300px;
  height: 40px;
  margin-top: 8px;
  cursor: pointer;
}

.custom-switch-btn input[type='checkbox'] {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: 0;
}

.custom-switch-btn input[type='checkbox'] + .back {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(63, 63, 63, 0.3);
  transition: background 150ms linear;
}

.custom-switch-btn input[type='checkbox'] + .back .toggle {
  display: block;
  position: absolute;
  content: ' ';
  background: linear-gradient(180deg, #ffd958 -30.88%, #ff964e 83.82%);
  width: 50%;
  height: 100%;
  transition: margin 130ms linear;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  overflow: hidden;
}

.custom-switch-btn input[type='checkbox'] + .back .toggle:before {
  position: absolute;
  content: '';
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, rgba(30, 87, 153, 0) 0%, rgba(84, 102, 135, 0) 24%, rgba(255, 150, 78, 1) 100%);
  right: 0;
  z-index: 1;
}

.custom-switch-btn input[type='checkbox']:checked + .back .toggle {
  margin-left: 130px;
  background: linear-gradient(180deg, #0cf540 -30.88%, #237d04 83.82%);
  cursor: pointer;
}

.btn-primary,
.btn-primary.MuiButtonBase-root {
  background: #fdb72e;
  color: #000;
  border-radius: 30px;
  font-weight: 600;
  border: 1px solid transparent;
  padding: 0.5rem 1.5rem;
}

.btn-primary.Mui-disabled,
.btn-primary.Mui-disabled:hover {
  filter: grayscale(10);
  opacity: 0.5;
  pointer-events: none;
  background-color: #fdb72e !important;
  color: #fff !important;
}

.theme-select {
  width: 100%;
}

.theme-select .MuiMenuItem-root {
  /* font-family: "Ubuntu", sans-serif; */
}

.theme-select .MuiFormLabel-root {
  color: #ffff;
  /* font-family: "Ubuntu", sans-serif; */
  font-weight: 700;
}

.theme-select .MuiSvgIcon-root {
  color: #fff;
}

.theme-select .MuiInputBase-root {
  background: #191919;
  border-radius: 30px;
  color: #fff;
  min-width: 200px;
}

@media (max-width: 960px) {
  .theme-select .MuiInputBase-root {
    min-width: 100%;
  }

  .theme-select .MuiFormControl-root {
    width: 100%;
  }
}

.theme-select .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #fdb72e;
  border-radius: 30px;
}

.btn-primary:hover,
.btn-primary.MuiButtonBase-root:hover {
  background: transparent;
  border-color: #fdb72e;
  color: #fdb72e;
}

.btn-secondary,
.btn-secondary.MuiButtonBase-root {
  background: transparent;
  color: #fff;
  border-radius: 30px;
  font-weight: 600;
  border: 2px solid #fdb72e;
  padding: 0.5rem 1.5rem;
}

.btn-secondary.MuiButtonBase-root.Mui-disabled {
  color: #fdb72e !important;
  opacity: 0.5;
}

.btn-secondary.MuiButtonBase-root:hover {
  background: #fdb72e;
  border-color: #fdb72e;
  color: #000;
}

.text-btn,
.text-btn.MuiButtonBase-root {
  color: #d3d3d3;
  font-weight: 600;
}

.custom-switch-btn input[type='checkbox']:checked + .back .toggle:before {
  position: absolute;
  content: '';
  width: 20px;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(30, 87, 153, 0) 0%,
    rgba(30, 87, 152, 0) 1%,
    rgba(34, 120, 23, 1) 87%,
    rgba(35, 125, 4, 1) 100%
  );
  right: 0;
  z-index: 1;
}

.custom-switch-btn input[type='checkbox'] + .back .toggle:before {
  position: absolute;
  content: '';
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, rgba(30, 87, 153, 0) 0%, rgba(84, 102, 135, 0) 24%, rgba(255, 150, 78, 1) 100%);
  right: 0;
  z-index: 1;
}

.custom-switch-btn .label {
  display: block;
  position: absolute;
  width: 50%;
  color: #ddd;
  line-height: 80px;
  text-align: center;
  font-size: 2em;
}

.custom-switch-btn .label.on {
  left: -30px;
}

.custom-switch-btn .label.off {
  right: 30px;
  justify-content: center !important;
  height: 100%;
}

.custom-switch-btn input[type='checkbox'] + .back .label.off,
.custom-switch-btn input[type='checkbox']:checked + .back .label.on,
.custom-switch-btn input[type='checkbox']:checked + .back .label.off {
  color: #fff;
}

.vertical-switch {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 60px;
}

.vertical-switch__slider {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  background: #4e4d4d;
  border: 1px solid #00000008;
  border-radius: 10px;
}

.vertical-switch__slider:before {
  position: absolute;
  content: '';
  bottom: 0;
  left: calc((100% - 30px) / 2);
  width: 30px;
  height: 30px;
  border-radius: 30px;
  background: linear-gradient(180deg, #ffd958 -30.88%, #ff964e 83.82%);
  transition: all 400ms cubic-bezier(1, 0, 0.01, 1.01);
}

.vertical-switch > input[type='checkbox'] {
  display: none;
}

.provider-menu-options {
  text-align: center;
}

.provider-menu-options img {
  width: 5rem;
  margin: 0 auto;
}

.vertical-switch > input[type='checkbox']:checked + .vertical-switch__slider:before {
  transform: translateY(calc(100% - 70px));
  background: linear-gradient(180deg, #0cf540 -30.88%, #237d04 83.82%);
}

*::-webkit-scrollbar-thumb {
  background: #fdb72e;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  background: #ffd85861;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 0.625rem;
}

.ribbon {
  width: 146px;
  height: 100px;
  overflow: hidden;
  position: absolute;
}

.ribbon span {
  position: absolute;
  display: block;
  width: 140px;
  padding: 4px 0;
  background-color: #aad840;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  text-align: center;
  text-transform: capitalize;
}

.ribbon-top-left,
.ribbon-gradient {
  top: 0px;
  left: 0px;
}

.ribbon-top-left span,
.ribbon-gradient span {
  right: 42px;
  top: 28px;
  transform: rotate(-45deg);
}

.ribbon-gradient span {
  background: linear-gradient(45deg, rgba(129, 77, 1, 1) 0%, rgba(251, 227, 7, 1) 50%, rgba(129, 77, 1, 1) 100%);
  text-shadow: 1px 1px 9px rgb(0 0 0 / 82%);
}

.slick-prev,
.slick-next {
  height: 40px;
  width: 40px;
  background: rgba(11, 85, 229, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 100%;
  z-index: 1;
}

.slick-prev:before {
  font-size: 0;
  background: url('../public/slide-arrow-left.png');
  background-repeat: no-repeat;
  background-size: 20px;
  height: 20px;
  width: 20px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.slick-next:before {
  font-size: 0;
  background: url('../public/slide-arrow-right.png');
  background-repeat: no-repeat;
  background-size: 20px;
  height: 20px;
  width: 20px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.custom-menu-item .MuiPaper-rounded {
  box-shadow: none !important;
}

.custom-menu-item .MuiMenu-list {
  background-color: #000;
  color: #fff;
}

#spin {
  display: none;
}

.no-spinners {
  -moz-appearance: textfield;
}

.no-spinners::-webkit-outer-spin-button,
.no-spinners::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  /* -webkit-box-shadow: 0 0 0 30px #0A0A57 inset !important; */
  -webkit-background-clip: text;
  -webkit-text-fill-color: #ffffff !important;
}

:-webkit-autofill {
  background-color: #3f3f3f !important;
  box-shadow: 0 0 0 2rem #3f3f3f inset !important;
  color: #fff !important;
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition: background-color 0s 6000000s, color 0s 6000000s !important;
  color: #fff !important;
}

.pac-container {
  z-index: 9999;
}

.inner-page-container {
  max-width: 74.6875rem;
  margin: 0 auto;
}

.phone-input-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  position: relative;
  margin: 1rem 0;
}

.phone-input-wrap .amount-input {
  margin: 0;
}

.phone-input-wrap .theme-select .MuiInputBase-root {
  background: #272727;
  padding: 0;
  min-height: 3.1875rem;
}

.phone-input-wrap .theme-select .MuiOutlinedInput-input {
  min-width: 1.875rem;
}

.phone-input-wrap .theme-select .MuiSelect-select {
  padding: 0.625rem;
}

.phone-input-wrap .inputError {
  position: absolute;
  /* left: -4.375rem; */
}

.custom-select div {
  background: transparent;
  border-color: #494949 !important;
  color: #fff;
  box-shadow: none;
}

.custom-select .css-1nmdiq5-menu {
  background-color: #494949;
}

.custom-select .css-tr4s17-option {
  cursor: pointer;
  background-color: #494949 !important;
}

.custom-select-paper li {
  color: #fff;
}

.custom-select-paper {
  background-color: #0c2320 !important;
}

.custom-select-paper .MuiMenuItem-root {
  font-weight: 600;
}

.custom-select-paper .MuiMenuItem-root.Mui-selected {
  background-color: #fdb72e !important;
  color: black !important;
}

.modal-open {
  overflow: hidden !important;
}

@keyframes mymove {
  50% {
    transform: scale(2);
    opacity: 0;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.my-custom-switch {
  position: relative;
  display: inline-block;
  width: 35px;
  height: 18px;
}

.my-custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.my-custom-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #838281;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.my-custom-switch .slider:before {
  position: absolute;
  content: '';
  height: 20px;
  width: 20px;
  left: -2px;
  bottom: -2px;
  background-color: #838281;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

/* .my-custom-switch input:checked + .slider {
  background-color: #2196f3;
}

.my-custom-switch input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
} */

.my-custom-switch input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
  background: #fdb72e;
}

/* Rounded sliders */
.my-custom-switch .slider.round {
  border-radius: 34px;
}

.my-custom-switch .slider.round:before {
  border-radius: 50%;
}

.tmf-jackpot {
  animation: tmfbounce 1s infinite;
}

@keyframes tmfbounce {
  0%,
  100% {
    transform: translate(-52%, 0%) scale(0.96);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translate(-52%, 0%) scale(1);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes scrollVertical {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.MuiMenu-list {
  color: #fff;
  /* '&:hover': { backgroundColor: '#333' } */
}
